<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الصالة الرياضية{% endblock %}</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="نظام شامل لإدارة الصالات الرياضية مع إدارة المشتركين والمبيعات والحضور">
    <meta name="theme-color" content="#006699">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Spring GYM">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="Spring GYM">

    <!-- <PERSON><PERSON> Manifest -->
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">

    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='icons/icon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='icons/icon-16x16.png') }}">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='icons/icon-180x180.png') }}">
    <link rel="mask-icon" href="{{ url_for('static', filename='icons/safari-pinned-tab.svg') }}" color="#006699">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Noto Sans Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}" defer></script>

    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- PWA Manifest -->
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">
    <meta name="theme-color" content="{{ theme_color | default('#006699') }}">

    <!-- Apply dynamic theme color -->
    <style>
        :root {
            --theme-color: {{ theme_color | default('#006699') }};
            --theme-color-rgb: {{ (theme_color | default('#006699'))[1:] | int(base=16) // 65536 }}, {{ ((theme_color | default('#006699'))[1:] | int(base=16) % 65536) // 256 }}, {{ (theme_color | default('#006699'))[1:] | int(base=16) % 256 }};
            --theme-hover: {{ theme_color | default('#004477') }};
        }
    </style>

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Noto Sans Arabic', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-arabic text-gray-800 min-h-screen">
    <!-- Mobile Navigation Overlay -->
    <div id="mobile-nav-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

    <!-- Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo and Title -->
                <div class="flex items-center space-x-3 space-x-reverse">
                    <img src="{{ logo_path | default(url_for('static', filename='logo.png')) }}"
                         alt="Logo" class="w-10 h-10 rounded-full object-cover">
                    <div class="hidden sm:block">
                        <h1 class="text-lg font-bold text-[var(--theme-color)] truncate max-w-48">
                            {{ client_name | default('نظام إدارة الصالة') }}
                        </h1>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex">
                    <ul class="flex items-center space-x-1 space-x-reverse">
                        <li><a href="{{ url_for('home') }}" class="nav-link"><i class="fas fa-home"></i> الرئيسية</a></li>
                        {% if session.can_search %}
                        <li><a href="{{ url_for('search') }}" class="nav-link"><i class="fas fa-search"></i> البحث</a></li>
                        {% endif %}
                        {% if session.can_add_person %}
                        <li><a href="{{ url_for('add_member') }}" class="nav-link"><i class="fas fa-user-plus"></i> إضافة مشترك</a></li>
                        {% endif %}
                        <li><a href="{{ url_for('list_members') }}" class="nav-link"><i class="fas fa-users"></i> المشتركين</a></li>
                        {% if session.role == 'admin' %}
                        <li class="relative group">
                            <button class="nav-link flex items-center">
                                <i class="fas fa-cog"></i> الإدارة <i class="fas fa-chevron-down mr-1"></i>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                                <a href="{{ url_for('manage_sales') }}" class="dropdown-link"><i class="fas fa-cash-register"></i> المبيعات</a>
                                <a href="{{ url_for('manage_products') }}" class="dropdown-link"><i class="fas fa-box"></i> المنتجات</a>
                                <a href="{{ url_for('manage_attendance') }}" class="dropdown-link"><i class="fas fa-calendar-check"></i> الحضور</a>
                                <a href="{{ url_for('inactive_members') }}" class="dropdown-link"><i class="fas fa-user-slash"></i> المشتركين غير النشطين</a>
                                <a href="{{ url_for('inactive_people') }}" class="dropdown-link"><i class="fas fa-user-times"></i> الإشتراكات المنتهية</a>
                                <a href="{{ url_for('user_logs_filter') }}" class="dropdown-link"><i class="fas fa-history"></i> سجل المستخدمين</a>
                                <a href="{{ url_for('manage_users') }}" class="dropdown-link"><i class="fas fa-users-cog"></i> إدارة المستخدمين</a>
                                <a href="{{ url_for('system_settings') }}" class="dropdown-link"><i class="fas fa-cogs"></i> إعدادات النظام</a>
                            </div>
                        </li>
                        {% endif %}
                        {% if session.can_reports %}
                        <li><a href="{{ url_for('reports') }}" class="nav-link"><i class="fas fa-chart-bar"></i> التقارير</a></li>
                        {% endif %}
                        <li class="relative group">
                            <button class="nav-link flex items-center">
                                <i class="fas fa-user"></i> {{ session.username | default('المستخدم') }} <i class="fas fa-chevron-down mr-1"></i>
                            </button>
                            <div class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                                <a href="{{ url_for('change_password') }}" class="dropdown-link"><i class="fas fa-key"></i> تغيير كلمة المرور</a>
                                <a href="{{ url_for('logout') }}" class="dropdown-link text-red-600"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                            </div>
                        </li>
                    </ul>
                </nav>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-btn" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="fas fa-bars text-xl text-[var(--theme-color)]"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation Sidebar -->
    <nav id="mobile-nav" class="fixed top-0 right-0 h-full w-80 bg-white shadow-xl transform translate-x-full transition-transform duration-300 z-50 lg:hidden">
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <img src="{{ logo_path | default(url_for('static', filename='logo.png')) }}"
                         alt="Logo" class="w-8 h-8 rounded-full object-cover">
                    <h2 class="text-lg font-bold text-[var(--theme-color)]">{{ session.username | default('المستخدم') }}</h2>
                </div>
                <button id="mobile-menu-close" class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="fas fa-times text-xl text-gray-600"></i>
                </button>
            </div>
        </div>

        <div class="p-4 space-y-2 overflow-y-auto h-full pb-20">
            <a href="{{ url_for('home') }}" class="mobile-nav-link"><i class="fas fa-home"></i> الرئيسية</a>
            {% if session.can_search %}
            <a href="{{ url_for('search') }}" class="mobile-nav-link"><i class="fas fa-search"></i> البحث</a>
            {% endif %}
            {% if session.can_add_person %}
            <a href="{{ url_for('add_member') }}" class="mobile-nav-link"><i class="fas fa-user-plus"></i> إضافة مشترك</a>
            {% endif %}
            <a href="{{ url_for('list_members') }}" class="mobile-nav-link"><i class="fas fa-users"></i> المشتركين</a>

            {% if session.role == 'admin' %}
            <div class="border-t border-gray-200 pt-4 mt-4">
                <h3 class="text-sm font-semibold text-gray-500 mb-2 px-3">الإدارة</h3>
                <a href="{{ url_for('manage_sales') }}" class="mobile-nav-link"><i class="fas fa-cash-register"></i> المبيعات</a>
                <a href="{{ url_for('manage_products') }}" class="mobile-nav-link"><i class="fas fa-box"></i> المنتجات</a>
                <a href="{{ url_for('manage_attendance') }}" class="mobile-nav-link"><i class="fas fa-calendar-check"></i> الحضور</a>
                <a href="{{ url_for('inactive_members') }}" class="mobile-nav-link"><i class="fas fa-user-slash"></i> المشتركين غير النشطين</a>
                <a href="{{ url_for('inactive_people') }}" class="mobile-nav-link"><i class="fas fa-user-times"></i> الإشتراكات المنتهية</a>
                <a href="{{ url_for('user_logs_filter') }}" class="mobile-nav-link"><i class="fas fa-history"></i> سجل المستخدمين</a>
                <a href="{{ url_for('manage_users') }}" class="mobile-nav-link"><i class="fas fa-users-cog"></i> إدارة المستخدمين</a>
                <a href="{{ url_for('system_settings') }}" class="mobile-nav-link"><i class="fas fa-cogs"></i> إعدادات النظام</a>
            </div>
            {% endif %}

            {% if session.can_reports %}
            <div class="border-t border-gray-200 pt-4 mt-4">
                <a href="{{ url_for('reports') }}" class="mobile-nav-link"><i class="fas fa-chart-bar"></i> التقارير</a>
            </div>
            {% endif %}

            <div class="border-t border-gray-200 pt-4 mt-4">
                <a href="{{ url_for('change_password') }}" class="mobile-nav-link"><i class="fas fa-key"></i> تغيير كلمة المرور</a>
                <a href="{{ url_for('logout') }}" class="mobile-nav-link text-red-600"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
            </div>
        </div>
    </nav>
    <!-- Main Content -->
    <main class="flex-1">
        <div class="container mx-auto px-4 py-6">
            <!-- License Warning -->
            {% if session.get('license_warning') %}
            <div class="alert-warning mb-4">
                <i class="fas fa-exclamation-triangle"></i>
                {{ session.get('license_warning') }}
            </div>
            {% endif %}

            <!-- Flash Messages -->
            {% for message in get_flashed_messages(with_categories=true) %}
            <div class="alert-{{ 'success' if message[0] == 'success' else 'error' }} mb-4">
                <i class="fas fa-{{ 'check-circle' if message[0] == 'success' else 'exclamation-circle' }}"></i>
                {{ message[1] }}
            </div>
            {% endfor %}

            <!-- Page Content -->
            <div class="bg-white shadow-sm rounded-xl p-4 sm:p-6 min-h-[calc(100vh-200px)]">
                {% block content %}{% endblock %}
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-[var(--theme-color)] text-white py-6 mt-auto">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center md:text-right">
                <div>
                    <h3 class="font-semibold mb-2">{{ client_name | default('نظام إدارة الصالة') }}</h3>
                    <p class="text-sm opacity-90">نظام متكامل لإدارة الصالات الرياضية</p>
                </div>
                <div>
                    <p class="text-sm"><i class="fas fa-calendar-alt"></i> {{ today }}</p>
                    <p class="text-sm"><i class="fas fa-user"></i> {{ current_user | default('زائر') }}</p>
                </div>
                <div>
                    <p class="text-sm opacity-90">تم التطوير بواسطة فريق التطوير</p>
                    <p class="text-xs opacity-75">جميع الحقوق محفوظة © 2024</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Spinner -->
    <div id="loading-spinner" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3 space-x-reverse">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--theme-color)]"></div>
            <span class="text-gray-700">جاري التحميل...</span>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Mobile Navigation
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileNav = document.getElementById('mobile-nav');
        const mobileNavOverlay = document.getElementById('mobile-nav-overlay');
        const mobileMenuClose = document.getElementById('mobile-menu-close');

        function openMobileNav() {
            mobileNav.classList.remove('translate-x-full');
            mobileNavOverlay.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        }

        function closeMobileNav() {
            mobileNav.classList.add('translate-x-full');
            mobileNavOverlay.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }

        mobileMenuBtn?.addEventListener('click', openMobileNav);
        mobileMenuClose?.addEventListener('click', closeMobileNav);
        mobileNavOverlay?.addEventListener('click', closeMobileNav);

        // Auto-hide alerts
        document.querySelectorAll('.alert-success, .alert-error, .alert-warning').forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            }, 5000);
        });

        // Loading spinner for forms
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', () => {
                document.getElementById('loading-spinner').classList.remove('hidden');
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.group')) {
                document.querySelectorAll('.group').forEach(group => {
                    group.classList.remove('hover');
                });
            }
        });
    </script>

    <!-- PWA Service Worker Registration -->
    <script>
        // Register service worker for PWA functionality
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/static/sw.js')
                    .then(function(registration) {
                        console.log('Service Worker registered successfully:', registration.scope);

                        // Check for updates
                        registration.addEventListener('updatefound', function() {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', function() {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New version available
                                    if (confirm('تحديث جديد متوفر. هل تريد إعادة تحميل الصفحة؟')) {
                                        newWorker.postMessage({ type: 'SKIP_WAITING' });
                                        window.location.reload();
                                    }
                                }
                            });
                        });
                    })
                    .catch(function(error) {
                        console.log('Service Worker registration failed:', error);
                    });
            });

            // Listen for service worker messages
            navigator.serviceWorker.addEventListener('message', function(event) {
                if (event.data && event.data.type === 'CACHE_UPDATED') {
                    console.log('Cache updated');
                }
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', function(e) {
            console.log('PWA install prompt triggered');
            e.preventDefault();
            deferredPrompt = e;

            // Show custom install button if needed
            const installButton = document.getElementById('pwa-install-button');
            if (installButton) {
                installButton.style.display = 'block';
                installButton.addEventListener('click', function() {
                    deferredPrompt.prompt();
                    deferredPrompt.userChoice.then(function(choiceResult) {
                        if (choiceResult.outcome === 'accepted') {
                            console.log('User accepted the PWA install prompt');
                        } else {
                            console.log('User dismissed the PWA install prompt');
                        }
                        deferredPrompt = null;
                        installButton.style.display = 'none';
                    });
                });
            }
        });

        // PWA Install Success
        window.addEventListener('appinstalled', function(e) {
            console.log('PWA was installed successfully');
            const installButton = document.getElementById('pwa-install-button');
            if (installButton) {
                installButton.style.display = 'none';
            }
        });

        // Online/Offline Status
        function updateOnlineStatus() {
            const statusElement = document.getElementById('connection-status');
            if (statusElement) {
                if (navigator.onLine) {
                    statusElement.textContent = 'متصل';
                    statusElement.className = 'text-green-600';
                } else {
                    statusElement.textContent = 'غير متصل';
                    statusElement.className = 'text-red-600';
                }
            }
        }

        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);
        updateOnlineStatus(); // Initial check
    </script>
</body>
</html>