{% extends "layout.html" %}
{% block content %}
<h2>📋 التقارير</h2>
{% if session.get('success') %}
<div class="alert alert-success">{{ session.get('success') }}</div>
{% endif %}
<h3>تقرير الاشتراكات</h3>
<table>
    <thead>
        <tr>
            <th>نوع الاشتراك</th>
            <th>عدد المشتركين</th>
        </tr>
    </thead>
    <tbody>
        {% for row in subscription_report %}
        <tr>
            <td>{{ row.SubscriptionType }}</td>
            <td>{{ row.Count }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<h3>تقرير المبيعات</h3>
<table>
    <thead>
        <tr>
            <th>التاريخ</th>
            <th>إجمالي المبيعات</th>
        </tr>
    </thead>
    <tbody>
        {% for row in sales_report %}
        <tr>
            <td>{{ row.SaleDate }}</td>
            <td>{{ row.TotalSales }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}