{% extends "layout.html" %}
{% block content %}
<h2>إدارة الحضور</h2>
{% if session.get('success') %}
<div class="alert alert-success">{{ session.get('success') }}</div>
{% endif %}
<form method="POST">
    <label>المشترك:</label>
    <select name="member_id" required>
        {% for m in members %}
        <option value="{{ m.ID }}">{{ m.Name }}</option>
        {% endfor %}
    </select>
    <label>الإجراء:</label>
    <select name="action" required>
        <option value="check_in">تسجيل دخول</option>
        <option value="check_out">تسجيل خروج</option>
    </select>
    <button type="submit">💾 تسجيل</button>
</form>
<table>
    <thead>
        <tr>
            <th>المشترك</th>
            <th>التاريخ</th>
            <th>وقت الدخول</th>
            <th>وقت الخروج</th>
        </tr>
    </thead>
    <tbody>
        {% for a in attendance_records %}
        <tr>
            <td>{{ a.Name }}</td>
            <td>{{ a.AttendanceDate }}</td>
            <td>{{ a.CheckInTime }}</td>
            <td>{{ a.CheckOutTime or 'غير مسجل' }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}