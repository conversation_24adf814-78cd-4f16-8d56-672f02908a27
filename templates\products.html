{% extends "layout.html" %}
{% block content %}
<h2>إدارة المنتجات</h2>
{% if session.get('success') %}
<div class="alert alert-success">{{ session.get('success') }}</div>
{% endif %}
<form method="POST">
    <label>اسم المنتج:</label>
    <input type="text" name="name" required>
    <label>السعر:</label>
    <input type="number" step="0.01" name="price" required>
    <label>الكمية المتاحة:</label>
    <input type="number" name="quantity" required>
    <button type="submit">💾 إضافة منتج</button>
</form>
<table>
    <thead>
        <tr>
            <th>الاسم</th>
            <th>السعر</th>
            <th>الكمية المتاحة</th>
            <th>الإجراءات</th>
        </tr>
    </thead>
    <tbody>
        {% for p in products %}
        <tr>
            <td>{{ p.Name }}</td>
            <td>{{ p.Price }}</td>
            <td>{{ p.QuantityAvailable }}</td>
            <td>
                <form method="POST" action="{{ url_for('delete_product', product_id=p.ID) }}">
                    <button type="submit">🗑️ حذف</button>
                </form>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}