{% extends "layout.html" %}
{% block content %}
<h2>إدارة المبيعات</h2>
{% if session.get('success') %}
<div class="alert alert-success">{{ session.get('success') }}</div>
{% endif %}
<form method="POST" id="saleForm">
    <label>المشترك:</label>
    <select name="member_id" required>
        {% for m in members %}
        <option value="{{ m.ID }}">{{ m.Name }}</option>
        {% endfor %}
    </select>
    <label>المنتجات:</label>
    <div id="products">
        <div class="product-entry">
            <select name="product_id" class="product-select">
                {% for p in products %}
                <option value="{{ p.ID }}" data-price="{{ p.Price }}">{{ p.Name }} ({{ p.Price }})</option>
                {% endfor %}
            </select>
            <input type="number" name="quantity" min="1" required>
        </div>
    </div>
    <button type="button" onclick="addProduct()">إضافة منتج آخر</button>
    <label>المدفوع:</label>
    <input type="number" step="0.01" name="paid_amount" required>
    <button type="submit">💾 تسجيل بيع</button>
</form>
<table>
    <thead>
        <tr>
            <th>رقم الفاتورة</th>
            <th>التاريخ</th>
            <th>المشترك</th>
            <th>الإجمالي</th>
            <th>المدفوع</th>
            <th>المتبقي</th>
            <th>الإجراءات</th>
        </tr>
    </thead>
    <tbody>
        {% for s in sales %}
        <tr>
            <td>{{ s.InvoiceNumber }}</td>
            <td>{{ s.SaleDate }}</td>
            <td>{{ s.Name }}</td>
            <td>{{ s.TotalAmount }}</td>
            <td>{{ s.PaidAmount }}</td>
            <td>{{ s.RemainingAmount }}</td>
            <td>
                <a href="{{ url_for('generate_invoice_pdf', sale_id=s.ID) }}">📄 فاتورة</a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<script>
function addProduct() {
    const productEntry = document.querySelector('.product-entry').cloneNode(true);
    productEntry.querySelector('input[name="quantity"]').value = '';
    document.getElementById('products').appendChild(productEntry);
}
document.getElementById('saleForm').addEventListener('submit', function(e) {
    const products = [];
    document.querySelectorAll('.product-entry').forEach(entry => {
        const productId = entry.querySelector('select').value;
        const quantity = entry.querySelector('input[name="quantity"]').value;
        products.push({ id: productId, quantity: parseInt(quantity) });
    });
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'products';
    input.value = JSON.stringify(products);
    this.appendChild(input);
});
</script>
{% endblock %}