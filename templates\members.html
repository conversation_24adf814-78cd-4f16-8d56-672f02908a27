{% extends "layout.html" %}
{% block title %}قائمة المشتركين - نظام إدارة الصالة الرياضية{% endblock %}
{% block content %}
<!-- Page Header -->
<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
    <div>
        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            <i class="fas fa-users text-[var(--theme-color)] ml-2"></i>
            قائمة المشتركين
        </h1>
        <p class="text-gray-600">إدارة وعرض جميع المشتركين في الصالة</p>
    </div>
    {% if session.can_add_person %}
    <div class="mt-4 sm:mt-0">
        <a href="{{ url_for('add_member') }}" class="btn-primary">
            <i class="fas fa-user-plus"></i>
            إضافة مشترك جديد
        </a>
    </div>
    {% endif %}
</div>

<!-- Success Message -->
{% if request.args.get('success_message') %}
<div class="alert-success mb-6">
    <i class="fas fa-check-circle"></i>
    {{ request.args.get('success_message') }}
</div>
{% endif %}

<!-- Search and Filter Section -->
<div class="card mb-8">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-search ml-2"></i>
            البحث والتصفية
        </h3>
    </div>
    <div class="card-body">
        <form method="GET" id="searchForm">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user text-gray-400 ml-1"></i>
                        الاسم
                    </label>
                    <input type="text"
                           name="name"
                           placeholder="ابحث بالاسم..."
                           value="{{ request.args.get('name', '') }}"
                           class="form-input">
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-phone text-gray-400 ml-1"></i>
                        الهاتف
                    </label>
                    <input type="text"
                           name="phone"
                           placeholder="ابحث بالهاتف..."
                           value="{{ request.args.get('phone', '') }}"
                           class="form-input">
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-toggle-on text-gray-400 ml-1"></i>
                        حالة الاشتراك
                    </label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="فعّال" {% if request.args.get('status') == 'فعّال' %}selected{% endif %}>فعّال</option>
                        <option value="قارب على الانتهاء" {% if request.args.get('status') == 'قارب على الانتهاء' %}selected{% endif %}>قارب على الانتهاء</option>
                        <option value="منتهي" {% if request.args.get('status') == 'منتهي' %}selected{% endif %}>منتهي</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label opacity-0">البحث</label>
                    <div class="flex space-x-2 space-x-reverse">
                        <button type="submit" class="btn-primary flex-1">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <a href="{{ url_for('list_members') }}" class="btn-outline flex-1 text-center">
                            <i class="fas fa-times"></i>
                            مسح
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Results Summary -->
<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
    <div class="text-sm text-gray-600 mb-2 sm:mb-0">
        <i class="fas fa-info-circle ml-1"></i>
        عرض {{ members|length }} من المشتركين
    </div>
    <div class="flex items-center space-x-2 space-x-reverse">
        <button id="viewToggle" class="btn-outline text-sm">
            <i class="fas fa-th-list" id="viewIcon"></i>
            <span id="viewText">عرض البطاقات</span>
        </button>
    </div>
</div>

{% if members %}
<!-- Desktop Table View -->
<div id="tableView" class="hidden lg:block">
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>النوع</th>
                    <th>نوع الاشتراك</th>
                    <th>البداية</th>
                    <th>النهاية</th>
                    <th>قيمة الاشتراك</th>
                    <th>المدفوع</th>
                    <th>المتبقي</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for m in members %}
                <tr>
                    <td class="font-medium">{{ m.Name }}</td>
                    <td>{{ m.Phone }}</td>
                    <td>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                     {% if m.Gender == 'ذكر' %}bg-blue-100 text-blue-800{% else %}bg-pink-100 text-pink-800{% endif %}">
                            <i class="fas fa-{{ 'mars' if m.Gender == 'ذكر' else 'venus' }} ml-1"></i>
                            {{ m.Gender }}
                        </span>
                    </td>
                    <td>{{ m.SubscriptionType }}</td>
                    <td>{{ m.StartDate }}</td>
                    <td>{{ m.EndDate }}</td>
                    <td class="font-medium">{{ m.SubscriptionValue }}</td>
                    <td class="text-green-600 font-medium">{{ m.PaidAmount }}</td>
                    <td class="{% if m.RemainingAmount > 0 %}text-red-600{% else %}text-green-600{% endif %} font-medium">
                        {{ m.RemainingAmount }}
                    </td>
                    <td>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                     {% if m.status == 'فعّال' %}bg-green-100 text-green-800
                                     {% elif m.status == 'قارب على الانتهاء' %}bg-yellow-100 text-yellow-800
                                     {% else %}bg-red-100 text-red-800{% endif %}">
                            <i class="fas fa-circle ml-1"></i>
                            {{ m.status }}
                        </span>
                    </td>
                    <td>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <a href="{{ url_for('edit_member', member_id=m.ID) }}"
                               class="text-blue-600 hover:text-blue-800 p-1 rounded"
                               title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{{ url_for('generate_member_qr', member_id=m.ID) }}"
                               class="text-purple-600 hover:text-purple-800 p-1 rounded"
                               title="QR Code">
                                <i class="fas fa-qrcode"></i>
                            </a>
                            <a href="{{ url_for('delete_member', member_id=m.ID) }}"
                               class="text-red-600 hover:text-red-800 p-1 rounded"
                               title="حذف"
                               onclick="return confirm('هل أنت متأكد من حذف هذا المشترك؟')">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Mobile Card View -->
<div id="cardView" class="lg:hidden">
    <div class="space-y-4">
        {% for m in members %}
        <div class="card">
            <div class="card-body">
                <!-- Member Header -->
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ m.Name }}</h3>
                        <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                            <i class="fas fa-phone"></i>
                            <span>{{ m.Phone }}</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                     {% if m.Gender == 'ذكر' %}bg-blue-100 text-blue-800{% else %}bg-pink-100 text-pink-800{% endif %}">
                            <i class="fas fa-{{ 'mars' if m.Gender == 'ذكر' else 'venus' }} ml-1"></i>
                            {{ m.Gender }}
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                     {% if m.status == 'فعّال' %}bg-green-100 text-green-800
                                     {% elif m.status == 'قارب على الانتهاء' %}bg-yellow-100 text-yellow-800
                                     {% else %}bg-red-100 text-red-800{% endif %}">
                            <i class="fas fa-circle ml-1"></i>
                            {{ m.status }}
                        </span>
                    </div>
                </div>

                <!-- Member Details -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <span class="text-sm text-gray-500">نوع الاشتراك</span>
                        <p class="font-medium">{{ m.SubscriptionType }}</p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">قيمة الاشتراك</span>
                        <p class="font-medium">{{ m.SubscriptionValue }}</p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">تاريخ البداية</span>
                        <p class="font-medium">{{ m.StartDate }}</p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">تاريخ النهاية</span>
                        <p class="font-medium">{{ m.EndDate }}</p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">المدفوع</span>
                        <p class="font-medium text-green-600">{{ m.PaidAmount }}</p>
                    </div>
                    <div>
                        <span class="text-sm text-gray-500">المتبقي</span>
                        <p class="font-medium {% if m.RemainingAmount > 0 %}text-red-600{% else %}text-green-600{% endif %}">
                            {{ m.RemainingAmount }}
                        </p>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center justify-end space-x-2 space-x-reverse pt-4 border-t border-gray-200">
                    <a href="{{ url_for('edit_member', member_id=m.ID) }}"
                       class="btn-outline text-sm">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </a>
                    <a href="{{ url_for('generate_member_qr', member_id=m.ID) }}"
                       class="btn-outline text-sm">
                        <i class="fas fa-qrcode"></i>
                        QR
                    </a>
                    <a href="{{ url_for('delete_member', member_id=m.ID) }}"
                       class="btn-danger text-sm"
                       onclick="return confirm('هل أنت متأكد من حذف هذا المشترك؟')">
                        <i class="fas fa-trash"></i>
                        حذف
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

{% else %}
<!-- No Results -->
<div class="no-results">
    <div class="no-results-icon">
        <i class="fas fa-users"></i>
    </div>
    <h3 class="no-results-title">لا توجد نتائج</h3>
    <p class="no-results-description">لم يتم العثور على أي مشتركين يطابقون معايير البحث</p>
    {% if session.can_add_person %}
    <div class="mt-6">
        <a href="{{ url_for('add_member') }}" class="btn-primary">
            <i class="fas fa-user-plus"></i>
            إضافة مشترك جديد
        </a>
    </div>
    {% endif %}
</div>
{% endif %}

<script>
// View toggle functionality
const viewToggle = document.getElementById('viewToggle');
const tableView = document.getElementById('tableView');
const cardView = document.getElementById('cardView');
const viewIcon = document.getElementById('viewIcon');
const viewText = document.getElementById('viewText');

let isCardView = window.innerWidth < 1024; // Default to card view on mobile

function updateView() {
    if (isCardView) {
        tableView.classList.add('hidden');
        cardView.classList.remove('hidden');
        viewIcon.className = 'fas fa-table';
        viewText.textContent = 'عرض الجدول';
    } else {
        tableView.classList.remove('hidden');
        cardView.classList.add('hidden');
        viewIcon.className = 'fas fa-th-list';
        viewText.textContent = 'عرض البطاقات';
    }
}

viewToggle.addEventListener('click', () => {
    isCardView = !isCardView;
    updateView();
});

// Initialize view
updateView();

// Handle window resize
window.addEventListener('resize', () => {
    if (window.innerWidth < 1024) {
        isCardView = true;
        updateView();
    }
});
</script>
{% endblock %}