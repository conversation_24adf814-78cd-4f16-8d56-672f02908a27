/* Import Noto Sans Arabic for better Arabic typography */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
    --theme-color: #006699;
    --theme-hover: #004477;
    --theme-light: #e6f3ff;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Base Styles */
html, body {
    direction: rtl;
    font-family: 'Noto Sans Arabic', sans-serif;
    scroll-behavior: smooth;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Navigation Styles */
.nav-link {
    @apply px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-white hover:bg-[var(--theme-color)] transition-all duration-200 flex items-center space-x-2 space-x-reverse;
}

.dropdown-link {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-[var(--theme-color)] transition-colors duration-150 flex items-center space-x-2 space-x-reverse;
}

.mobile-nav-link {
    @apply block px-3 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-[var(--theme-color)] transition-colors duration-150 flex items-center space-x-3 space-x-reverse;
}

/* Alert Styles */
.alert-success {
    @apply bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg flex items-center space-x-2 space-x-reverse animate-fade-in;
}

.alert-error {
    @apply bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg flex items-center space-x-2 space-x-reverse animate-fade-in;
}

.alert-warning {
    @apply bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded-lg flex items-center space-x-2 space-x-reverse animate-fade-in animate-pulse;
}

/* Table Styles - Mobile First */
.table-container {
    @apply overflow-x-auto rounded-lg shadow-sm border border-gray-200;
}

table {
    @apply w-full border-collapse bg-white;
}

th {
    @apply bg-[var(--theme-color)] text-white px-4 py-3 text-right text-sm font-semibold uppercase tracking-wider;
}

td {
    @apply px-4 py-3 text-sm text-gray-900 border-b border-gray-200;
}

tr:hover {
    @apply bg-gray-50;
}

/* Mobile Table Cards */
@media (max-width: 768px) {
    .table-mobile-card {
        @apply block bg-white rounded-lg shadow-sm border border-gray-200 mb-4 p-4;
    }

    .table-mobile-card .card-header {
        @apply font-semibold text-[var(--theme-color)] mb-2 pb-2 border-b border-gray-200;
    }

    .table-mobile-card .card-row {
        @apply flex justify-between py-1;
    }

    .table-mobile-card .card-label {
        @apply text-gray-600 text-sm;
    }

    .table-mobile-card .card-value {
        @apply text-gray-900 text-sm font-medium;
    }
}

/* Button Styles */
.btn-primary {
    @apply bg-[var(--theme-color)] hover:bg-[var(--theme-hover)] text-white font-medium py-2.5 px-6 rounded-lg transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-[var(--theme-color)] focus:ring-opacity-50;
}

.btn-secondary {
    @apply bg-gray-500 hover:bg-gray-600 text-white font-medium py-2.5 px-6 rounded-lg transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50;
}

.btn-success {
    @apply bg-green-500 hover:bg-green-600 text-white font-medium py-2.5 px-6 rounded-lg transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50;
}

.btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-white font-medium py-2.5 px-6 rounded-lg transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50;
}

.btn-outline {
    @apply border-2 border-[var(--theme-color)] text-[var(--theme-color)] hover:bg-[var(--theme-color)] hover:text-white font-medium py-2.5 px-6 rounded-lg transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-[var(--theme-color)] focus:ring-opacity-50;
}

/* Legacy button support */
button, input[type="submit"] {
    @apply btn-primary;
}

button:hover, input[type="submit"]:hover {
    @apply bg-[var(--theme-hover)] transform -translate-y-0.5;
}

/* Form Styles */
.form-group {
    @apply mb-4;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
    @apply w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--theme-color)] focus:ring-opacity-50 focus:border-[var(--theme-color)] transition-all duration-200 bg-white;
}

.form-input:disabled {
    @apply bg-gray-100 text-gray-500 cursor-not-allowed;
}

.form-select {
    @apply w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--theme-color)] focus:ring-opacity-50 focus:border-[var(--theme-color)] transition-all duration-200 bg-white;
}

.form-textarea {
    @apply w-full px-3 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--theme-color)] focus:ring-opacity-50 focus:border-[var(--theme-color)] transition-all duration-200 bg-white resize-y min-h-[100px];
}

.form-error {
    @apply text-red-600 text-sm mt-1;
}

.form-help {
    @apply text-gray-500 text-sm mt-1;
}

/* Legacy form input support */
input[type="text"], input[type="password"], input[type="number"], input[type="date"], input[type="email"], input[type="tel"], select, input[type="color"] {
    @apply form-input;
}

textarea {
    @apply form-textarea;
}

/* Dashboard Styles */
.dashboard-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
}

.stat-card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-300 transform hover:-translate-y-1;
}

.stat-card-icon {
    @apply w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl mb-4;
}

.stat-card-title {
    @apply text-sm font-medium text-gray-600 mb-2;
}

.stat-card-value {
    @apply text-2xl font-bold text-gray-900;
}

.stat-card-change {
    @apply text-sm font-medium mt-2;
}

.stat-card-change.positive {
    @apply text-green-600;
}

.stat-card-change.negative {
    @apply text-red-600;
}

/* Card Styles */
.card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-title {
    @apply text-lg font-semibold text-gray-900;
}

.card-body {
    @apply p-6;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Animations */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in;
}

.animate-slide-in {
    animation: slideIn 0.3s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes bounceIn {
    0% { opacity: 0; transform: scale(0.3); }
    50% { opacity: 1; transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { opacity: 1; transform: scale(1); }
}

/* Loading States */
.loading {
    @apply opacity-50 pointer-events-none;
}

.skeleton {
    @apply bg-gray-200 animate-pulse rounded;
}

/* No Results */
.no-results {
    @apply text-center py-12;
}

.no-results-icon {
    @apply w-16 h-16 mx-auto text-gray-400 mb-4;
}

.no-results-title {
    @apply text-lg font-medium text-gray-900 mb-2;
}

.no-results-description {
    @apply text-gray-500;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-link span {
        @apply hidden;
    }
}

@media (max-width: 768px) {
    /* Hide desktop navigation */
    .desktop-nav {
        @apply hidden;
    }

    /* Mobile table adjustments */
    .table-container {
        @apply overflow-x-auto;
    }

    table {
        @apply min-w-full text-sm;
    }

    th, td {
        @apply px-2 py-2;
    }

    /* Mobile form adjustments */
    .form-grid {
        @apply grid-cols-1;
    }

    /* Mobile dashboard */
    .dashboard-grid {
        @apply grid-cols-1 sm:grid-cols-2;
    }

    /* Mobile card spacing */
    .card-body {
        @apply p-4;
    }

    /* Mobile button adjustments */
    .btn-group {
        @apply flex-col space-y-2 space-x-0;
    }

    /* Mobile navigation adjustments */
    .mobile-nav {
        @apply w-full max-w-sm;
    }
}

@media (max-width: 640px) {
    /* Extra small screens */
    .container {
        @apply px-2;
    }

    .dashboard-grid {
        @apply grid-cols-1;
    }

    .stat-card {
        @apply p-4;
    }

    .card-header, .card-body, .card-footer {
        @apply px-4;
    }

    /* Mobile table cards */
    .table-responsive {
        @apply block;
    }

    .table-responsive table {
        @apply hidden;
    }

    .table-responsive .mobile-cards {
        @apply block;
    }
}

/* Print Styles */
@media print {
    .no-print {
        @apply hidden;
    }

    body {
        @apply text-black bg-white;
    }

    .card {
        @apply shadow-none border border-gray-300;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-300: #6b7280;
        --gray-400: #9ca3af;
        --gray-500: #d1d5db;
        --gray-600: #e5e7eb;
        --gray-700: #f3f4f6;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }
}

/* Utility Classes */
.text-truncate {
    @apply truncate;
}

.text-wrap {
    @apply break-words;
}

.border-dashed {
    border-style: dashed;
}

.cursor-pointer {
    @apply cursor-pointer;
}

.cursor-not-allowed {
    @apply cursor-not-allowed;
}

/* Focus Styles */
.focus-visible {
    @apply focus:outline-none focus:ring-2 focus:ring-[var(--theme-color)] focus:ring-opacity-50;
}

/* Enhanced Form Validation Styles */
.form-label.required::after {
    content: " *";
    color: #ef4444;
    font-weight: bold;
}

.form-input.error,
.form-select.error,
.form-textarea.error {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500;
}

.form-input.success,
.form-select.success,
.form-textarea.success {
    @apply border-green-500 focus:border-green-500 focus:ring-green-500;
}

.form-error {
    @apply text-red-600 text-sm mt-1 flex items-center;
}

.form-error::before {
    content: "⚠️";
    margin-left: 0.25rem;
}

/* Enhanced Table Styles */
.table-container {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
}

.table-container table {
    @apply w-full border-collapse;
}

.table-container th {
    @apply bg-[var(--theme-color)] text-white px-4 py-3 text-right font-semibold text-sm;
}

.table-container td {
    @apply px-4 py-3 text-sm border-b border-gray-100;
}

.table-container tr:hover {
    @apply bg-gray-50;
}

.table-container tr:last-child td {
    @apply border-b-0;
}

/* Enhanced No Results Component */
.no-results {
    @apply text-center py-16 px-8 bg-white rounded-xl shadow-sm border border-gray-200;
}

.no-results-icon {
    @apply text-6xl text-gray-300 mb-6;
}

.no-results-title {
    @apply text-xl font-semibold text-gray-900 mb-3;
}

.no-results-description {
    @apply text-gray-600 mb-8 max-w-md mx-auto;
}

/* Mobile Card View Enhancements */
@media (max-width: 1024px) {
    .mobile-card {
        @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-4;
    }

    .mobile-card-header {
        @apply flex items-start justify-between mb-4 pb-4 border-b border-gray-100;
    }

    .mobile-card-title {
        @apply text-lg font-semibold text-gray-900;
    }

    .mobile-card-subtitle {
        @apply text-sm text-gray-600 mt-1;
    }

    .mobile-card-body {
        @apply space-y-3;
    }

    .mobile-card-row {
        @apply flex justify-between items-center;
    }

    .mobile-card-label {
        @apply text-sm text-gray-600;
    }

    .mobile-card-value {
        @apply text-sm font-medium text-gray-900;
    }

    .mobile-card-actions {
        @apply flex items-center justify-end space-x-2 space-x-reverse pt-4 border-t border-gray-100;
    }
}

/* Enhanced Button Styles */
.btn-group {
    @apply flex items-center space-x-2 space-x-reverse;
}

.btn-sm {
    @apply py-1.5 px-3 text-sm;
}

.btn-lg {
    @apply py-3 px-8 text-lg;
}

.btn-icon {
    @apply p-2 rounded-lg;
}

/* Loading and Spinner Styles */
.spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-[var(--theme-color)];
}

.spinner-sm {
    @apply w-4 h-4;
}

.spinner-md {
    @apply w-6 h-6;
}

.spinner-lg {
    @apply w-8 h-8;
}

/* Status Badge Styles */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-error {
    @apply bg-red-100 text-red-800;
}

.badge-info {
    @apply bg-blue-100 text-blue-800;
}

.badge-gray {
    @apply bg-gray-100 text-gray-800;
}

/* Enhanced Animations */
.animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-subtle {
    animation: bounceSubtle 2s infinite;
}

@keyframes bounceSubtle {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover Effects */
.hover-scale {
    @apply transition-transform duration-200;
}

.hover-scale:hover {
    @apply transform scale-105;
}

.hover-shadow {
    @apply transition-shadow duration-200;
}

.hover-shadow:hover {
    @apply shadow-lg;
}

/* Glass Morphism Effects */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}