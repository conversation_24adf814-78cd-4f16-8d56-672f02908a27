{% extends "layout.html" %}
{% block title %}إعدادات النظام - Spring GYM{% endblock %}
{% block content %}

<h2>إعدادات النظام</h2>

{% if success_message %}
    <div style="background-color: #d4edda; padding: 10px; color: #155724; border-radius: 5px; margin-bottom: 15px;">
        ✅ {{ success_message }}
    </div>
{% endif %}

<form method="POST" enctype="multipart/form-data" style="background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); max-width: 600px; margin: auto;">
    <input type="hidden" name="current_logo" value="{{ settings.LogoPath }}">

    <div style="margin-bottom: 15px;">
        <label>اسم العميل:</label><br>
        <input type="text" name="client_name" value="{{ settings.ClientName }}" class="form-control">
    </div>

    <div style="margin-bottom: 15px;">
        <label>اختر اللون الرئيسي:</label><br>
        <div style="display: flex; gap: 12px; margin-top: 8px;">
            {% set palette = [
                {'color': '#2d3e50', 'name': 'أزرق أعمال'},
                {'color': '#4e73df', 'name': 'أزرق هادئ'},
                {'color': '#198754', 'name': 'أخضر داكن'},
                {'color': '#6c757d', 'name': 'رمادي أعمال'},
                {'color': '#795548', 'name': 'بني كلاسيكي'},
                {'color': '#343a40', 'name': 'أسود رمادي'}
            ] %}
            {% for item in palette %}
            <label style="cursor:pointer;">
                <input type="radio" name="theme_color" value="{{ item.color }}" style="display:none"
                    {% if settings.ThemeColor == item.color %}checked{% endif %}>
                <span title="{{ item.name }}"
                    style="
                        display:inline-block;
                        width:32px;height:32px;
                        border-radius:50%;
                        border: 2px solid {% if settings.ThemeColor == item.color %}#000{% else %}#ccc{% endif %};
                        background: {{ item.color }};
                        box-shadow: 0 1px 4px rgba(0,0,0,0.08);
                        vertical-align:middle;
                    ">
                </span>
            </label>
            {% endfor %}
        </div>
    </div>

    <div style="margin-bottom: 15px;">
        <label>شعار البرنامج:</label><br>
        {% if settings.LogoPath %}
            <img src="{{ url_for('static', filename=settings.LogoPath) }}" style="height: 60px; margin-bottom: 10px;"><br>
        {% endif %}
        <input type="file" name="logo" class="form-control">
    </div>

    <button type="submit" class="btn btn-primary">💾 حفظ التعديلات</button>
    <a href="/" class="btn btn-secondary">⬅️ العودة للرئيسية</a>
</form>

{% endblock %}