{% extends "layout.html" %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-[var(--theme-color)] mb-8 text-center">✏️ تعديل بيانات المشترك</h2>

    <!-- Edit Member Form -->
    <form method="POST" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-gray-700 font-semibold mb-2">الاسم:</label>
                <input type="text" name="name" value="{{ member.Name }}" required class="w-full">
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">رقم الهاتف:</label>
                <input type="text" name="phone" value="{{ member.Phone }}" required class="w-full">
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">تاريخ الميلاد:</label>
                <input type="date" name="birth_date" value="{{ member.BirthDate }}" class="w-full">
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">النوع:</label>
                <select name="gender" required class="w-full">
                    <option value="ذكر" {% if member.Gender == 'ذكر' %}selected{% endif %}>ذكر</option>
                    <option value="أنثى" {% if member.Gender == 'أنثى' %}selected{% endif %}>أنثى</option>
                </select>
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">نوع الاشتراك:</label>
                <input type="text" name="subscription_type" value="{{ member.SubscriptionType }}" required class="w-full">
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">تاريخ البداية:</label>
                <input type="date" name="start_date" value="{{ member.StartDate }}" required class="w-full">
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">تاريخ النهاية:</label>
                <input type="date" name="end_date" value="{{ member.EndDate }}" required class="w-full">
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">قيمة الاشتراك:</label>
                <input type="number" step="0.01" name="subscription_value" value="{{ member.SubscriptionValue }}" required class="w-full">
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">المدفوع:</label>
                <input type="number" step="0.01" name="paid_amount" value="{{ member.PaidAmount }}" required class="w-full">
            </div>
            <div class="md:col-span-2">
                <label class="block text-gray-700 font-semibold mb-2">ملاحظات:</label>
                <textarea name="notes" class="w-full min-h-[100px] resize-y">{{ member.Notes }}</textarea>
            </div>
        </div>
        <div class="flex space-x-4 space-x-reverse mt-6 justify-center">
            <button type="submit" class="bg-[var(--theme-color)] hover:bg-[var(--theme-hover)] text-white font-semibold py-2.5 px-6 rounded-lg transition-transform duration-200 transform hover:-translate-y-1">💾 حفظ التعديلات</button>
            <a href="{{ url_for('list_members') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2.5 px-6 rounded-lg transition-transform duration-200 transform hover:-translate-y-1">🔙 رجوع</a>
        </div>
    </form>
</div>
{% endblock %}