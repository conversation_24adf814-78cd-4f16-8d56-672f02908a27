{% extends "layout.html" %}
{% block title %}شاشة تغيير كلمة المرور - بيت الأنبا كاراس لذوي الهمم{% endblock %}
{% block content %}

<div class="change-password-container">
    <h2>🔒 تغيير كلمة المرور</h2>
    <p>من فضلك أدخل بياناتك لتحديث كلمة المرور الخاصة بك.</p>

    <!-- رسائل الخطأ والنجاح -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        {% for category, message in messages %}
        {% if category == 'error' %}
            <div class="message-error">{{ message }}</div>
        {% elif category == 'success' %}
            <div class="message-success">{{ message }}</div>
        {% endif %}
        {% endfor %}
    {% endif %}
    {% endwith %}


    <form method="post" class="password-form">
        <label for="old_password">كلمة المرور الحالية:</label>
        <div class="input-wrapper">
            <input type="password" id="old_password" name="old_password" required>
            <span onclick="toggleVisibility('old_password')" class="toggle-visibility">👁️</span>
        </div>

        <label for="new_password">كلمة المرور الجديدة:</label>
        <div class="input-wrapper">
            <input type="password" id="new_password" name="new_password" required>
            <span onclick="toggleVisibility('new_password')" class="toggle-visibility">👁️</span>
        </div>

        <label for="confirm_password">تأكيد كلمة المرور:</label>
        <div class="input-wrapper">
            <input type="password" id="confirm_password" name="confirm_password" required>
            <span onclick="toggleVisibility('confirm_password')" class="toggle-visibility">👁️</span>
        </div>

        <div class="submit-btn-container">
            <button type="submit" class="save-button">💾 حفظ التغييرات</button>
        </div>
    </form>
</div>

<!-- JavaScript لإظهار/إخفاء كلمة المرور -->
<script>
    function toggleVisibility(id) {
        const input = document.getElementById(id);
        input.type = input.type === 'password' ? 'text' : 'password';
    }
</script>

<style>
    .change-password-container {
        max-width: 400px;
        margin: 20px auto;
        background-color: #ffffff;
        padding: 20px 25px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        text-align: right;
        font-family: 'Tajawal', sans-serif;
    }

    .change-password-container h2 {
        text-align: center;
        color: #007BFF;
        margin-bottom: 10px;
    }

    .change-password-container p {
        text-align: center;
        color: #555;
        margin-bottom: 25px;
        font-size: 15px;
    }

    .message-error {
        background-color: #ffe6e6;
        color: #cc0000;
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 15px;
        text-align: center;
        font-weight: bold;
    }

    .message-success {
        background-color: #e6ffee;
        color: #006600;
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 15px;
        text-align: center;
        font-weight: bold;
    }

    .password-form label {
        display: block;
        margin-bottom: 6px;
        font-weight: bold;
        color: #333;
    }

    .input-wrapper {
        position: relative;
    }

    .input-wrapper input {
        width: 100%;
        padding: 10px 35px 10px 10px;
        margin-bottom: 15px;
        border: 1px solid #ccc;
        border-radius: 6px;
        font-size: 14px;
        direction: rtl;
    }

    .toggle-visibility {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        font-size: 18px;
        color: #888;
    }

    .submit-btn-container {
        text-align: center;
        margin-top: 10px;
    }

    .save-button {
        background-color: #007BFF;
        color: white;
        padding: 12px 20px;
        font-size: 16px;
        font-weight: bold;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .save-button:hover {
        background-color: #0056b3;
    }
</style>

<div style="margin-bottom: 50px;"></div>

{% endblock %}
