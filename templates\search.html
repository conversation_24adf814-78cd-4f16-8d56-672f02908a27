{% extends "layout.html" %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-[var(--theme-color)] mb-8 text-center">🔍 البحث عن المشتركين</h2>

    <!-- Search Form -->
    <form method="POST" class="mb-8 bg-white p-6 rounded-lg shadow-lg max-w-2xl mx-auto">
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
                <label class="block text-gray-700 font-semibold mb-2">الاسم:</label>
                <input type="text" name="name" placeholder="أدخل الاسم" class="w-full">
            </div>
            <div>
                <label class="block text-gray-700 font-semibold mb-2">الهاتف:</label>
                <input type="text" name="phone" placeholder="أدخل رقم الهاتف" class="w-full">
            </div>
            <div class="sm:col-span-2 flex justify-center">
                <button type="submit" class="bg-[var(--theme-color)] hover:bg-[var(--theme-hover)] text-white font-semibold py-2.5 px-6 rounded-lg transition-transform duration-200 transform hover:-translate-y-1">🔍 بحث</button>
            </div>
        </div>
    </form>

    <!-- Search Results -->
    {% if members %}
    <div class="overflow-x-auto">
        <table class="w-full border-collapse bg-white rounded-lg shadow-lg">
            <thead>
                <tr class="bg-[var(--theme-color)] text-white">
                    <th class="p-4">الاسم</th>
                    <th class="p-4">الهاتف</th>
                    <th class="p-4">النوع</th>
                    <th class="p-4">نوع الاشتراك</th>
                    <th class="p-4">البداية</th>
                    <th class="p-4">النهاية</th>
                    <th class="p-4">قيمة الاشتراك</th>
                    <th class="p-4">المدفوع</th>
                    <th class="p-4">المتبقي</th>
                    <th class="p-4">الحالة</th>
                    <th class="p-4">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for m in members %}
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                    <td class="p-4">{{ m.Name }}</td>
                    <td class="p-4">{{ m.Phone }}</td>
                    <td class="p-4">{{ m.Gender }}</td>
                    <td class="p-4">{{ m.SubscriptionType }}</td>
                    <td class="p-4">{{ m.StartDate }}</td>
                    <td class="p-4">{{ m.EndDate }}</td>
                    <td class="p-4">{{ m.SubscriptionValue }}</td>
                    <td class="p-4">{{ m.PaidAmount }}</td>
                    <td class="p-4">{{ m.RemainingAmount }}</td>
                    <td class="p-4">{{ m.status }}</td>
                    <td class="p-4 flex space-x-2 space-x-reverse">
                        <a href="{{ url_for('edit_member', member_id=m.ID) }}" class="text-[var(--theme-color)] hover:text-[var(--theme-hover)]">✏️</a>
                        <a href="{{ url_for('delete_member', member_id=m.ID) }}" class="text-red-600 hover:text-red-800" onclick="return confirm('هل أنت متأكد من حذف هذا المشترك؟')">🗑️</a>
                        <a href="{{ url_for('generate_member_qr', member_id=m.ID) }}" class="text-[var(--theme-color)] hover:text-[var(--theme-hover)]">📷 QR</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <p class="text-center text-gray-600 mt-6">لا توجد نتائج للبحث</p>
    {% endif %}
</div>
{% endblock %}