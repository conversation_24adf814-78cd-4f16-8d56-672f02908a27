<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تعديل صلاحيات {{ user.Username }}</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 40px;
            color: #333;
        }

        .container {
            max-width: 500px;
            margin: auto;
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        h2 {
            text-align: center;
            color: #007BFF;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        label {
            font-size: 15px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        input[type="checkbox"] {
            transform: scale(1.2);
        }

        button[type="submit"] {
            display: block;
            width: 100%;
            padding: 12px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button[type="submit"]:hover {
            background-color: #0056b3;
        }

        @media (max-width: 576px) {
            .container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <h2>تعديل صلاحيات المستخدم: {{ user.Username }}</h2>
    <form method="post">
        <div class="form-group">
            <label><input type="checkbox" name="can_search" {% if user.CanSearch %}checked{% endif %}> البحث</label>
        </div>
        <div class="form-group">
            <label><input type="checkbox" name="can_add_person" {% if user.CanAddPerson %}checked{% endif %}> إضافة فرد</label>
        </div>
        <div class="form-group">
            <label><input type="checkbox" name="can_edit" {% if user.CanEdit %}checked{% endif %}> تعديل بيانات</label>
        </div>
        <div class="form-group">
            <label><input type="checkbox" name="can_delete" {% if user.CanDelete %}checked{% endif %}> حذف بيانات</label>
        </div>
        <div class="form-group">
            <label><input type="checkbox" name="can_reports" {% if user.CanReports %}checked{% endif %}> الوصول للتقارير</label>
        </div>
        <div class="form-group">
            <label><input type="checkbox" name="can_manage_users" {% if user.CanManageUsers %}checked{% endif %}> إدارة المستخدمين</label>
        </div>

        <button type="submit">💾 حفظ الصلاحيات</button>
    </form>
</div>

</body>
</html>