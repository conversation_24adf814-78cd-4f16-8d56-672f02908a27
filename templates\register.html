{% extends "layout.html" %}
{% block title %} تسجيل مستخدم جديد - الأنبا كاراس لذوي الهمم{% endblock %}
{% block content %}

<style>
    body {
        font-family: 'Cairo', sans-serif;
        background-color: #674ced;
        padding: 30px;
    }

    h2 {
        text-align: center;
        color: #3c14de;
        margin-bottom: 30px;
    }

    .form-container {
        max-width: 500px;
        margin: auto;
        background-color: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    label {
        display: block;
        margin-top: 15px;
        font-weight: bold;
        color: #333;
    }

    input[type="text"],
    input[type="password"],
    select {
        width: 100%;
        padding: 10px 12px;
        margin-top: 6px;
        border: 1px solid #ccc;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }

    input:focus,
    select:focus {
        border-color: #007BFF;
        outline: none;
    }

    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-top: 6px;
    }

    .checkbox-group small {
        color: #888;
    }

    button[type="submit"] {
        margin-top: 25px;
        width: 100%;
        padding: 12px;
        background-color: #007BFF;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    button[type="submit"]:hover {
        background-color: #0056b3;
    }
</style>

<div class="form-container">
    <h2>تسجيل مستخدم جديد</h2>

    <form method="post">
        <label>اسم المستخدم:</label>
        <input type="text" name="username" required>

        <label>كلمة المرور:</label>
        <input type="password" name="password" required>

        <label>البحث:</label>
        <div class="checkbox-group">
            <input type="checkbox" name="can_search" checked>
            <span>مسموح</span>
        </div>

        <label>إضافة فرد جديد:</label>
        <div class="checkbox-group">
            <input type="checkbox" name="can_add_person">
            <span>غير مسموح افتراضيًا</span>
        </div>

        <label>تعديل فرد:</label>
        <div class="checkbox-group">
            <input type="checkbox" name="can_edit">
            <span>غير مسموح افتراضيًا</span>
        </div>

        <label>حذف فرد:</label>
        <div class="checkbox-group">
            <input type="checkbox" name="can_delete">
            <span>غير مسموح افتراضيًا</span>
        </div>

        <label>التقارير:</label>
        <div class="checkbox-group">
            <input type="checkbox" name="can_reports">
            <span>غير مسموح افتراضيًا</span>
        </div>

        <label>إدارة المستخدمين:</label>
        <div class="checkbox-group">
            <input type="checkbox" name="can_manage_users">
            <span>غير مسموح افتراضيًا</span>
        </div>

        <label>نوع الصلاحية:</label>
        <select name="role">
        <option value="admin">Admin</option>
        <option value="editor">Editor</option>
        <option value="viewer">Viewer</option>
        </select>

        <button type="submit">💾 حفظ</button>
    </form>
</div>

<!-- المساحة السفلية لتكون تحت المحتوى -->
<div style="margin-bottom: 50px;"></div>


{% endblock %}