{% extends "layout.html" %}
{% block title %}إضافة مشترك جديد - نظام إدارة الصالة الرياضية{% endblock %}
{% block content %}
<!-- Page Header -->
<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
    <div>
        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            <i class="fas fa-user-plus text-[var(--theme-color)] ml-2"></i>
            إضافة مشترك جديد
        </h1>
        <p class="text-gray-600">أدخل بيانات المشترك الجديد في النموذج أدناه</p>
    </div>
    <div class="mt-4 sm:mt-0">
        <a href="{{ url_for('list_members') }}" class="btn-outline">
            <i class="fas fa-arrow-right"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<!-- Success Message -->
{% if success %}
<div class="alert-success mb-6">
    <i class="fas fa-check-circle"></i>
    {{ success }}
</div>
{% endif %}

<!-- Add Member Form -->
<div class="max-w-4xl mx-auto">
    <form method="POST" id="addMemberForm" class="space-y-8">
        <!-- Personal Information Section -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user ml-2"></i>
                    المعلومات الشخصية
                </h3>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Name -->
                    <div class="form-group">
                        <label class="form-label required">
                            <i class="fas fa-user text-gray-400 ml-1"></i>
                            الاسم الكامل
                        </label>
                        <input type="text"
                               name="name"
                               id="name"
                               required
                               class="form-input"
                               placeholder="أدخل الاسم الكامل"
                               autocomplete="name">
                        <div class="form-error hidden" id="name-error"></div>
                    </div>

                    <!-- Phone -->
                    <div class="form-group">
                        <label class="form-label required">
                            <i class="fas fa-phone text-gray-400 ml-1"></i>
                            رقم الهاتف
                        </label>
                        <input type="tel"
                               name="phone"
                               id="phone"
                               required
                               class="form-input"
                               placeholder="05xxxxxxxx"
                               autocomplete="tel">
                        <div class="form-error hidden" id="phone-error"></div>
                        <div class="form-help">مثال: 0501234567</div>
                    </div>

                    <!-- Birth Date -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-birthday-cake text-gray-400 ml-1"></i>
                            تاريخ الميلاد
                        </label>
                        <input type="date"
                               name="birth_date"
                               id="birth_date"
                               class="form-input"
                               max="{{ today }}">
                        <div class="form-error hidden" id="birth_date-error"></div>
                    </div>

                    <!-- Gender -->
                    <div class="form-group">
                        <label class="form-label required">
                            <i class="fas fa-venus-mars text-gray-400 ml-1"></i>
                            النوع
                        </label>
                        <select name="gender" id="gender" required class="form-select">
                            <option value="">اختر النوع</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                        <div class="form-error hidden" id="gender-error"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Information Section -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-calendar-alt ml-2"></i>
                    معلومات الاشتراك
                </h3>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Subscription Type -->
                    <div class="form-group">
                        <label class="form-label required">
                            <i class="fas fa-tags text-gray-400 ml-1"></i>
                            نوع الاشتراك
                        </label>
                        <select name="subscription_type" id="subscription_type" required class="form-select">
                            <option value="">اختر نوع الاشتراك</option>
                            <option value="شهري">شهري</option>
                            <option value="ربع سنوي">ربع سنوي</option>
                            <option value="نصف سنوي">نصف سنوي</option>
                            <option value="سنوي">سنوي</option>
                            <option value="يومي">يومي</option>
                            <option value="أسبوعي">أسبوعي</option>
                        </select>
                        <div class="form-error hidden" id="subscription_type-error"></div>
                    </div>

                    <!-- Subscription Value -->
                    <div class="form-group">
                        <label class="form-label required">
                            <i class="fas fa-money-bill text-gray-400 ml-1"></i>
                            قيمة الاشتراك
                        </label>
                        <div class="relative">
                            <input type="number"
                                   name="subscription_value"
                                   id="subscription_value"
                                   required
                                   min="0"
                                   step="0.01"
                                   class="form-input pl-12"
                                   placeholder="0.00">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">ريال</div>
                        </div>
                        <div class="form-error hidden" id="subscription_value-error"></div>
                    </div>

                    <!-- Start Date -->
                    <div class="form-group">
                        <label class="form-label required">
                            <i class="fas fa-play text-gray-400 ml-1"></i>
                            تاريخ البداية
                        </label>
                        <input type="date"
                               name="start_date"
                               id="start_date"
                               required
                               class="form-input"
                               value="{{ today }}">
                        <div class="form-error hidden" id="start_date-error"></div>
                    </div>

                    <!-- End Date -->
                    <div class="form-group">
                        <label class="form-label required">
                            <i class="fas fa-stop text-gray-400 ml-1"></i>
                            تاريخ النهاية
                        </label>
                        <input type="date"
                               name="end_date"
                               id="end_date"
                               required
                               class="form-input">
                        <div class="form-error hidden" id="end_date-error"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Information Section -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-credit-card ml-2"></i>
                    معلومات الدفع
                </h3>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Paid Amount -->
                    <div class="form-group">
                        <label class="form-label required">
                            <i class="fas fa-hand-holding-usd text-gray-400 ml-1"></i>
                            المبلغ المدفوع
                        </label>
                        <div class="relative">
                            <input type="number"
                                   name="paid_amount"
                                   id="paid_amount"
                                   required
                                   min="0"
                                   step="0.01"
                                   class="form-input pl-12"
                                   placeholder="0.00">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">ريال</div>
                        </div>
                        <div class="form-error hidden" id="paid_amount-error"></div>
                    </div>

                    <!-- Remaining Amount (Auto-calculated) -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-calculator text-gray-400 ml-1"></i>
                            المبلغ المتبقي
                        </label>
                        <div class="relative">
                            <input type="number"
                                   id="remaining_amount"
                                   readonly
                                   class="form-input pl-12 bg-gray-50"
                                   placeholder="0.00">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">ريال</div>
                        </div>
                        <div class="form-help">يتم حساب المبلغ المتبقي تلقائياً</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information Section -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-sticky-note ml-2"></i>
                    معلومات إضافية
                </h3>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-comment text-gray-400 ml-1"></i>
                        ملاحظات
                    </label>
                    <textarea name="notes"
                              id="notes"
                              class="form-textarea"
                              placeholder="أدخل أي ملاحظات إضافية هنا..."
                              rows="4"></textarea>
                    <div class="form-help">ملاحظات اختيارية حول المشترك أو الاشتراك</div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-end space-y-3 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
            <button type="submit" class="btn-primary">
                <i class="fas fa-save"></i>
                حفظ المشترك
                <div id="submitSpinner" class="hidden animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            </button>
            <a href="{{ url_for('list_members') }}" class="btn-secondary">
                <i class="fas fa-times"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>

<style>
.form-label.required::after {
    content: " *";
    color: #ef4444;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('addMemberForm');
    const submitSpinner = document.getElementById('submitSpinner');

    // Form elements
    const nameInput = document.getElementById('name');
    const phoneInput = document.getElementById('phone');
    const birthDateInput = document.getElementById('birth_date');
    const genderSelect = document.getElementById('gender');
    const subscriptionTypeSelect = document.getElementById('subscription_type');
    const subscriptionValueInput = document.getElementById('subscription_value');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const paidAmountInput = document.getElementById('paid_amount');
    const remainingAmountInput = document.getElementById('remaining_amount');

    // Validation functions
    function showError(fieldId, message) {
        const errorElement = document.getElementById(fieldId + '-error');
        const inputElement = document.getElementById(fieldId);

        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }

        if (inputElement) {
            inputElement.classList.add('border-red-500');
            inputElement.classList.remove('border-green-500');
        }
    }

    function hideError(fieldId) {
        const errorElement = document.getElementById(fieldId + '-error');
        const inputElement = document.getElementById(fieldId);

        if (errorElement) {
            errorElement.classList.add('hidden');
        }

        if (inputElement) {
            inputElement.classList.remove('border-red-500');
            inputElement.classList.add('border-green-500');
        }
    }

    function validateName() {
        const value = nameInput.value.trim();
        if (!value) {
            showError('name', 'يرجى إدخال الاسم');
            return false;
        } else if (value.length < 2) {
            showError('name', 'الاسم قصير جداً');
            return false;
        } else {
            hideError('name');
            return true;
        }
    }

    function validatePhone() {
        const value = phoneInput.value.trim();
        const phoneRegex = /^(05|5)[0-9]{8}$/;

        if (!value) {
            showError('phone', 'يرجى إدخال رقم الهاتف');
            return false;
        } else if (!phoneRegex.test(value)) {
            showError('phone', 'رقم الهاتف غير صحيح (مثال: 0501234567)');
            return false;
        } else {
            hideError('phone');
            return true;
        }
    }

    function validateBirthDate() {
        const value = birthDateInput.value;
        if (value) {
            const birthDate = new Date(value);
            const today = new Date();
            const age = today.getFullYear() - birthDate.getFullYear();

            if (birthDate > today) {
                showError('birth_date', 'تاريخ الميلاد لا يمكن أن يكون في المستقبل');
                return false;
            } else if (age > 100) {
                showError('birth_date', 'تاريخ الميلاد غير منطقي');
                return false;
            } else {
                hideError('birth_date');
                return true;
            }
        }
        return true; // Optional field
    }

    function validateGender() {
        if (!genderSelect.value) {
            showError('gender', 'يرجى اختيار النوع');
            return false;
        } else {
            hideError('gender');
            return true;
        }
    }

    function validateSubscriptionType() {
        if (!subscriptionTypeSelect.value) {
            showError('subscription_type', 'يرجى اختيار نوع الاشتراك');
            return false;
        } else {
            hideError('subscription_type');
            return true;
        }
    }

    function validateSubscriptionValue() {
        const value = parseFloat(subscriptionValueInput.value);
        if (!subscriptionValueInput.value) {
            showError('subscription_value', 'يرجى إدخال قيمة الاشتراك');
            return false;
        } else if (value <= 0) {
            showError('subscription_value', 'قيمة الاشتراك يجب أن تكون أكبر من صفر');
            return false;
        } else {
            hideError('subscription_value');
            return true;
        }
    }

    function validateStartDate() {
        if (!startDateInput.value) {
            showError('start_date', 'يرجى إدخال تاريخ البداية');
            return false;
        } else {
            hideError('start_date');
            return true;
        }
    }

    function validateEndDate() {
        if (!endDateInput.value) {
            showError('end_date', 'يرجى إدخال تاريخ النهاية');
            return false;
        } else if (startDateInput.value && endDateInput.value) {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);

            if (endDate <= startDate) {
                showError('end_date', 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
                return false;
            } else {
                hideError('end_date');
                return true;
            }
        } else {
            hideError('end_date');
            return true;
        }
    }

    function validatePaidAmount() {
        const value = parseFloat(paidAmountInput.value);
        const subscriptionValue = parseFloat(subscriptionValueInput.value);

        if (!paidAmountInput.value) {
            showError('paid_amount', 'يرجى إدخال المبلغ المدفوع');
            return false;
        } else if (value < 0) {
            showError('paid_amount', 'المبلغ المدفوع لا يمكن أن يكون سالباً');
            return false;
        } else if (subscriptionValue && value > subscriptionValue) {
            showError('paid_amount', 'المبلغ المدفوع لا يمكن أن يكون أكبر من قيمة الاشتراك');
            return false;
        } else {
            hideError('paid_amount');
            return true;
        }
    }

    // Calculate remaining amount
    function calculateRemainingAmount() {
        const subscriptionValue = parseFloat(subscriptionValueInput.value) || 0;
        const paidAmount = parseFloat(paidAmountInput.value) || 0;
        const remaining = subscriptionValue - paidAmount;
        remainingAmountInput.value = remaining.toFixed(2);
    }

    // Auto-calculate end date based on subscription type
    function calculateEndDate() {
        if (startDateInput.value && subscriptionTypeSelect.value) {
            const startDate = new Date(startDateInput.value);
            let endDate = new Date(startDate);

            switch (subscriptionTypeSelect.value) {
                case 'يومي':
                    endDate.setDate(startDate.getDate() + 1);
                    break;
                case 'أسبوعي':
                    endDate.setDate(startDate.getDate() + 7);
                    break;
                case 'شهري':
                    endDate.setMonth(startDate.getMonth() + 1);
                    break;
                case 'ربع سنوي':
                    endDate.setMonth(startDate.getMonth() + 3);
                    break;
                case 'نصف سنوي':
                    endDate.setMonth(startDate.getMonth() + 6);
                    break;
                case 'سنوي':
                    endDate.setFullYear(startDate.getFullYear() + 1);
                    break;
            }

            endDateInput.value = endDate.toISOString().split('T')[0];
        }
    }

    // Event listeners
    nameInput.addEventListener('blur', validateName);
    phoneInput.addEventListener('blur', validatePhone);
    birthDateInput.addEventListener('blur', validateBirthDate);
    genderSelect.addEventListener('change', validateGender);
    subscriptionTypeSelect.addEventListener('change', function() {
        validateSubscriptionType();
        calculateEndDate();
    });
    subscriptionValueInput.addEventListener('input', function() {
        validateSubscriptionValue();
        calculateRemainingAmount();
    });
    startDateInput.addEventListener('change', function() {
        validateStartDate();
        calculateEndDate();
    });
    endDateInput.addEventListener('change', validateEndDate);
    paidAmountInput.addEventListener('input', function() {
        validatePaidAmount();
        calculateRemainingAmount();
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate all fields
        const isValid = validateName() &&
                       validatePhone() &&
                       validateBirthDate() &&
                       validateGender() &&
                       validateSubscriptionType() &&
                       validateSubscriptionValue() &&
                       validateStartDate() &&
                       validateEndDate() &&
                       validatePaidAmount();

        if (isValid) {
            // Show loading state
            submitSpinner.classList.remove('hidden');
            const submitButton = form.querySelector('button[type="submit"]');
            submitButton.disabled = true;

            // Submit the form
            form.submit();
        } else {
            // Scroll to first error
            const firstError = form.querySelector('.border-red-500');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        }
    });

    // Initialize calculations
    calculateRemainingAmount();
});
</script>
{% endblock %}