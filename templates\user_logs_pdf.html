<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: 'Cairo', sans-serif; direction: rtl; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #aaa; padding: 8px; text-align: center; }
        th { background-color: #007BFF; color: white; }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 13px;
            color: #777;
            border-top: 1px solid #ccc;
            padding-top: 20px;
            }
    </style>
</head>
<body>
    <h1 style="text-align:center;"><span>{{ client_name }}</span></h1>
    <h2 style="text-align:center;">تقرير أنشطة المستخدمين</h2>
    <p>الفترة من: {{ request.args.get('start_date') }} إلى {{ request.args.get('end_date') }}</p>
    <p style="text-align:left;">تاريخ التصدير: {{ today }}</p>
    <table>
        <thead>
            <tr>
                <th>اسم المستخدم</th>
                <th>العملية</th>
                <th>الشاشة</th>
                <th>اسم المعاملة</th>
                <th>الجهاز</th>
                <th>الوقت</th>
            </tr>
        </thead>
        <tbody>
            {% for log in logs %}
            <tr>
                <td>{{ log.Username }}</td>
                <td>{{ log.ActionType }}</td>
                <td>{{ log.ScreenName }}</td>
                <td>{{ log.TransactionName }}</td>
                <td>{{ log.DeviceName }}</td>
                <td>{{ log.Timestamp.strftime('%d/%m/%Y %H:%M') }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>


    <div class="footer">
    تم إعداد هذا التقرير بواسطة {{ session.username or session.role or 'النظام' }}<br>
    التاريخ: {{ today }}<br>
    © حقوق الطبع محفوظة - Spring Soft
    </div>

</body>
</html>
