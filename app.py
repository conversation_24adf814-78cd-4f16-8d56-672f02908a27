from flask import Flask, render_template, request, redirect, url_for, session, flash, make_response, send_file
import os
from config import get_db_connection
import pandas as pd
from io import BytesIO
import hashlib
from datetime import datetime, timedelta, date
import pdfkit
from werkzeug.utils import secure_filename
import qrcode
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import base64
from decimal import Decimal

app = Flask(__name__)
app.secret_key = 'your_secret_key_here'
app.config['UPLOAD_FOLDER'] = os.path.join('static', 'Uploads')
app.permanent_session_lifetime = timedelta(minutes=5)

# License checking
def check_license():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT ExpiryDate, IsActive FROM Validation WHERE IsActive = 1")
    row = cursor.fetchone()
    conn.close()
    if not row:
        return False, 0
    expiry_date = row.ExpiryDate
    today = datetime.now().date()
    remaining_days = (expiry_date - today).days
    return (remaining_days >= 0), remaining_days

@app.before_request
def check_license_middleware():
    exempt_routes = ['static', 'license_expired', 'favicon']
    if request.endpoint and any(r in request.endpoint for r in exempt_routes):
        return
    valid, days = check_license()
    if not valid:
        return redirect(url_for('license_expired'))
    if days <= 10:
        session['license_warning'] = f"⚠️ سينتهي ترخيص البرنامج خلال {days} يومًا. يرجى إعادة التفعيل."
    else:
        session.pop('license_warning', None)

# License activation
SECRET_KEY = b'your-32-byte-secret-key!!'
key = b'SpringSoftKey1234'
iv = b'SpringSoftIV__000'

def decrypt_activation_code(code):
    try:
        decoded = base64.b64decode(code)
        cipher = AES.new(SECRET_KEY, AES.MODE_CBC, iv)
        decrypted = unpad(cipher.decrypt(decoded), AES.block_size)
        date_str = decrypted.decode('utf-8')
        expiry_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        return expiry_date
    except Exception as e:
        print(f"Decryption failed: {e}")
        return None

@app.route('/license_expired', methods=['GET', 'POST'])
def license_expired():
    if request.method == 'POST':
        code = request.form.get('activation_code')
        expiry_date = decrypt_activation_code(code)
        if expiry_date:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("UPDATE Validation SET ExpiryDate = ?, IsActive = 1", (expiry_date,))
            conn.commit()
            conn.close()
            flash("✅ تم التفعيل بنجاح حتى تاريخ: " + expiry_date.strftime('%Y-%m-%d'), 'success')
            return redirect(url_for('login'))
        else:
            flash("❌ رمز التفعيل غير صحيح أو منتهي الصلاحية.", 'error')
    return render_template('license_expired.html')

@app.route('/activate', methods=['GET', 'POST'])
def activate():
    if request.method == 'POST':
        code = request.form['activation_code']
        expiry_date = decrypt_activation_code(code)
        if expiry_date:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("UPDATE Validation SET ExpiryDate = ?, IsActive = 1", (expiry_date,))
            conn.commit()
            conn.close()
            flash("✅ تم تفعيل البرنامج حتى " + expiry_date.strftime('%Y-%m-%d'), "success")
            return redirect(url_for('login'))
        else:
            flash("❌ رمز التفعيل غير صالح.", "error")
    return render_template('activate.html')

# File upload route
@app.route('/Uploads/<path:filename>')
def uploaded_file(filename):
    return send_file(os.path.join(app.config['UPLOAD_FOLDER'], filename))


@app.context_processor
def inject_settings():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT ClientName, ThemeColor, LogoPath FROM Validation WHERE IsActive = 1")
    settings = cursor.fetchone()
    conn.close()
    return dict(
        client_name=settings.ClientName if settings else 'اسم العميل',
        theme_color=settings.ThemeColor if settings else '#006699',
        logo_path=settings.LogoPath if settings else url_for('static', filename='logo.png'),
        today=datetime.now().strftime('%Y-%m-%d'),
        current_user=session.get('username', 'زائر')
    )

# Session and context processors
@app.context_processor
def inject_session_data():
    return {
        'session': session,
        'current_user': session.get('username') if 'username' in session else None,
        'current_role': session.get('role') if 'role' in session else None,
        'logged_in': 'user_id' in session
    }



@app.before_request
def make_session_permanent():
    session.permanent = True

# Home route with error handling for BirthDate
@app.route('/')
def home():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT COUNT(*) FROM Members WHERE IsActive = 1")
        total_members = cursor.fetchone()[0] or 0
        cursor.execute("SELECT COUNT(*) FROM Members WHERE EndDate < GETDATE() AND IsActive = 1")
        total_expired = cursor.fetchone()[0] or 0
        cursor.execute("SELECT COUNT(DISTINCT ResponsibleServerName) FROM People WHERE ResponsibleServerName IS NOT NULL AND ResponsibleServerName <> '' AND (IsInactive IS NULL OR IsInactive = 0)")
        total_servers = cursor.fetchone()[0] or 0
        cursor.execute("SELECT AVG(DATEDIFF(DAY, StartDate, GETDATE())) FROM Members WHERE StartDate IS NOT NULL AND IsActive = 1")
        avg_subscription = round(cursor.fetchone()[0] or 0)
        cursor.execute("SELECT MAX(DATEDIFF(DAY, StartDate, GETDATE())) FROM Members WHERE StartDate IS NOT NULL AND IsActive = 1")
        max_subscription = cursor.fetchone()[0] or 0
        avg_age = min_age = max_age = 0
        try:
            cursor.execute("SELECT AVG(DATEDIFF(DAY, BirthDate, GETDATE())/365.25) FROM Members WHERE BirthDate IS NOT NULL AND IsActive = 1")
            avg_age = round(cursor.fetchone()[0] or 0)
            cursor.execute("SELECT MIN(DATEDIFF(DAY, BirthDate, GETDATE())/365.25) FROM Members WHERE BirthDate IS NOT NULL AND IsActive = 1")
            min_age = round(cursor.fetchone()[0] or 0)
            cursor.execute("SELECT MAX(DATEDIFF(DAY, BirthDate, GETDATE())/365.25) FROM Members WHERE BirthDate IS NOT NULL AND IsActive = 1")
            max_age = round(cursor.fetchone()[0] or 0)
        except pyodbc.ProgrammingError:
            pass  # Skip age calculations if BirthDate column is missing
    except Exception as e:
        flash(f"❌ خطأ في جلب البيانات: {str(e)}", "error")
        total_members = total_expired = total_servers = avg_subscription = max_subscription = avg_age = min_age = max_age = 0
    finally:
        conn.close()
    return render_template(
        'index.html',
        total_people=total_members,
        total_notactive_people=total_expired,
        total_servers=total_servers,
        avg_stay=avg_subscription,
        max_stay=max_subscription,
        avg_age=avg_age,
        min_age=min_age,
        max_age=max_age
    )

# Login route
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT ID, Username, PasswordHash, Role, CanSearch, CanAddPerson, CanEdit, CanDelete, CanReports, CanManageUsers
            FROM Users WHERE Username = ?
        """, (username,))
        user = cursor.fetchone()
        conn.close()
        hashed_pass = hashlib.sha256(password.encode()).hexdigest()
        if user and user.PasswordHash == hashed_pass:
            session['user_id'] = user.ID
            session['role'] = user.Role
            session['username'] = user.Username
            session['can_search'] = bool(user.CanSearch)
            session['can_add_person'] = bool(user.CanAddPerson)
            session['can_edit'] = bool(user.CanEdit)
            session['can_delete'] = bool(user.CanDelete)
            session['can_reports'] = bool(user.CanReports)
            session['can_manage_users'] = bool(user.CanManageUsers)
            ip_address = request.headers.get('X-Forwarded-For', request.remote_addr)
            log_user_action(user.ID, user.Username, "Login", "Login Page", "", ip_address)
            return redirect(url_for('home'))
        else:
            flash("⚠️ اسم المستخدم أو كلمة المرور غير صحيحة", "error")
            return redirect(url_for('login'))
    return render_template('login.html', login_page=True)

# Logout route
@app.route('/logout')
def logout():
    if 'user_id' in session:
        log_user_action(session['user_id'], session['username'], "Logout", "Logout", "", request.remote_addr)
        session.clear()
    return redirect(url_for('login'))

# Search route
@app.route('/search', methods=['GET', 'POST'])
def search():
    if 'user_id' not in session or not session.get('can_search', False):
        return "غير مصرح لك بدخول هذه الصفحة", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    members = []
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        phone = request.form.get('phone', '').strip()
        query = "SELECT ID, Name, Phone, Gender, SubscriptionType, StartDate, EndDate, SubscriptionValue, PaidAmount, RemainingAmount FROM Members WHERE IsActive = 1"
        params = []
        if name:
            query += " AND Name LIKE ?"
            params.append(f"%{name}%")
        if phone:
            query += " AND Phone LIKE ?"
            params.append(f"%{phone}%")
        cursor.execute(query, params)
        rows = cursor.fetchall()
        today = datetime.today().date()
        for row in rows:
            end_date = row.EndDate
            days_left = (end_date - today).days
            status = "فعّال" if days_left > 7 else "قارب على الانتهاء" if days_left >= 0 else "منتهي"
            members.append({
                'ID': row.ID,
                'Name': row.Name,
                'Phone': row.Phone,
                'Gender': row.Gender,
                'SubscriptionType': row.SubscriptionType,
                'StartDate': row.StartDate,
                'EndDate': row.EndDate,
                'SubscriptionValue': row.SubscriptionValue,
                'PaidAmount': row.PaidAmount,
                'RemainingAmount': row.RemainingAmount,
                'status': status
            })
    conn.close()
    return render_template('search.html', members=members)

# Member management routes
@app.route('/add-member', methods=['GET', 'POST'])
def add_member():
    if 'role' not in session or session['role'] not in ['admin', 'editor']:
        return "غير مصرح لك", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    if request.method == 'POST':
        name = request.form['name']
        phone = request.form['phone']
        birth_date = request.form.get('birth_date') or None
        gender = request.form['gender']
        subscription_type = request.form['subscription_type']
        start_date = request.form['start_date']
        end_date = request.form['end_date']
        notes = request.form['notes']
        subscription_value = float(request.form['subscription_value'])
        paid_amount = float(request.form['paid_amount'])
        remaining_amount = subscription_value - paid_amount
        cursor.execute("""
            INSERT INTO Members (Name, Phone, BirthDate, Gender, SubscriptionType, StartDate, EndDate, Notes, SubscriptionValue, PaidAmount, RemainingAmount, IsActive)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
        """, (name, phone, birth_date, gender, subscription_type, start_date, end_date, notes, subscription_value, paid_amount, remaining_amount))
        conn.commit()
        conn.close()
        flash("✅ تم حفظ المشترك بنجاح", "success")
        return redirect(url_for('list_members'))
    conn.close()
    return render_template('add_member.html')

@app.route('/edit-member/<int:member_id>', methods=['GET', 'POST'])
def edit_member(member_id):
    if 'role' not in session or session['role'] not in ['admin', 'editor']:
        return "غير مصرح لك", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    if request.method == 'POST':
        name = request.form['name']
        phone = request.form['phone']
        birth_date = request.form.get('birth_date') or None
        gender = request.form['gender']
        subscription_type = request.form['subscription_type']
        start_date = request.form['start_date']
        end_date = request.form['end_date']
        notes = request.form['notes']
        subscription_value = float(request.form['subscription_value'])
        paid_amount = float(request.form['paid_amount'])
        remaining_amount = subscription_value - paid_amount
        cursor.execute("""
            UPDATE Members SET
            Name = ?, Phone = ?, BirthDate = ?, Gender = ?, SubscriptionType = ?,
            StartDate = ?, EndDate = ?, Notes = ?,
            SubscriptionValue = ?, PaidAmount = ?, RemainingAmount = ?
            WHERE ID = ?
        """, (name, phone, birth_date, gender, subscription_type, start_date, end_date, notes, subscription_value, paid_amount, remaining_amount, member_id))
        conn.commit()
        conn.close()
        flash("✅ تم تعديل المشترك بنجاح", "success")
        return redirect(url_for('list_members'))
    cursor.execute("SELECT * FROM Members WHERE ID = ? AND IsActive = 1", (member_id,))
    member = cursor.fetchone()
    conn.close()
    if not member:
        flash("❌ المشترك غير موجود", "error")
        return redirect(url_for('list_members'))
    return render_template('edit_member.html', member=member)

@app.route('/delete-member/<int:member_id>', methods=['POST'])
def delete_member(member_id):
    if 'role' not in session or session['role'] not in ['admin', 'editor']:
        return "غير مصرح لك", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("UPDATE Members SET IsActive = 0 WHERE ID = ?", (member_id,))
    conn.commit()
    conn.close()
    flash("✅ تم حذف المشترك بنجاح", "success")
    return redirect(url_for('list_members'))

@app.route('/members', methods=['GET'])
def list_members():
    conn = get_db_connection()
    cursor = conn.cursor()
    name = request.args.get('name', '').strip()
    phone = request.args.get('phone', '').strip()
    status_filter = request.args.get('status', '')
    query = """
        SELECT ID, Name, Phone, Gender, SubscriptionType, StartDate, EndDate, SubscriptionValue, PaidAmount, RemainingAmount
        FROM Members WHERE IsActive = 1
    """
    params = []
    if name:
        query += " AND Name LIKE ?"
        params.append(f"%{name}%")
    if phone:
        query += " AND Phone LIKE ?"
        params.append(f"%{phone}%")
    cursor.execute(query, params)
    rows = cursor.fetchall()
    conn.close()
    today = datetime.today().date()
    members = []
    for row in rows:
        end_date = row.EndDate
        days_left = (end_date - today).days
        status = "فعّال" if days_left > 7 else "قارب على الانتهاء" if days_left >= 0 else "منتهي"
        if status_filter and status != status_filter:
            continue
        members.append({
            'ID': row.ID,
            'Name': row.Name,
            'Phone': row.Phone,
            'Gender': row.Gender,
            'SubscriptionType': row.SubscriptionType,
            'StartDate': row.StartDate,
            'EndDate': row.EndDate,
            'SubscriptionValue': row.SubscriptionValue,
            'PaidAmount': row.PaidAmount,
            'RemainingAmount': row.RemainingAmount,
            'status': status
        })
    return render_template('members.html', members=members)

# Product management routes
@app.route('/products', methods=['GET', 'POST'])
def manage_products():
    if 'role' not in session or session['role'] != 'admin':
        return "غير مصرح لك بدخول هذه الصفحة", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    if request.method == 'POST':
        name = request.form['name']
        price = float(request.form['price'])
        quantity = int(request.form['quantity'])
        cursor.execute("INSERT INTO Products (Name, Price, QuantityAvailable) VALUES (?, ?, ?)", (name, price, quantity))
        conn.commit()
        flash("✅ تم إضافة المنتج بنجاح", "success")
        return redirect(url_for('manage_products'))
    cursor.execute("SELECT ID, Name, Price, QuantityAvailable FROM Products")
    products = cursor.fetchall()
    conn.close()
    return render_template('products.html', products=products)

@app.route('/delete-product/<int:product_id>', methods=['POST'])
def delete_product(product_id):
    if 'role' not in session or session['role'] != 'admin':
        return "غير مصرح لك", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("DELETE FROM Products WHERE ID = ?", (product_id,))
    conn.commit()
    conn.close()
    flash("✅ تم حذف المنتج بنجاح", "success")
    return redirect(url_for('manage_products'))

# Sales management routes
@app.route('/sales', methods=['GET', 'POST'])
def manage_sales():
    if 'role' not in session or session['role'] != 'admin':
        return "غير مصرح لك بدخول هذه الصفحة", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    if request.method == 'POST':
        member_id = int(request.form['member_id'])
        products = json.loads(request.form['products'])  # Expected format: [{"id": 1, "quantity": 2}, ...]
        sale_date = datetime.now().date()
        invoice_number = f"INV-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        total_amount = Decimal('0.0')  # Initialize as Decimal
        product_details = []
        for item in products:
            cursor.execute("SELECT Price, QuantityAvailable FROM Products WHERE ID = ?", (item['id'],))
            product = cursor.fetchone()
            if product and product.QuantityAvailable >= item['quantity']:
                total_amount += Decimal(str(product.Price)) * Decimal(str(item['quantity']))
                product_details.append({"id": item['id'], "quantity": item['quantity']})
                cursor.execute("UPDATE Products SET QuantityAvailable = QuantityAvailable - ? WHERE ID = ?", (item['quantity'], item['id']))
        paid_amount = Decimal(request.form['paid_amount'])  # Convert to Decimal
        remaining_amount = total_amount - paid_amount
        cursor.execute("""
            INSERT INTO Sales (InvoiceNumber, SaleDate, MemberID, Products, TotalAmount, PaidAmount, RemainingAmount, UserID)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (invoice_number, sale_date, member_id, json.dumps(product_details), float(total_amount), float(paid_amount), float(remaining_amount), session['user_id']))
        conn.commit()
        flash("✅ تم تسجيل البيع بنجاح", "success")
        return redirect(url_for('manage_sales'))
    cursor.execute("SELECT ID, Name FROM Members WHERE IsActive = 1")
    members = cursor.fetchall()
    cursor.execute("SELECT ID, Name, Price, QuantityAvailable FROM Products")
    products = cursor.fetchall()
    cursor.execute("""
        SELECT s.ID, s.InvoiceNumber, s.SaleDate, m.Name, s.TotalAmount, s.PaidAmount, s.RemainingAmount
        FROM Sales s JOIN Members m ON s.MemberID = m.ID
    """)
    sales = cursor.fetchall()
    conn.close()
    return render_template('sales.html', members=members, products=products, sales=sales)


@app.route('/sales/invoice/<int:sale_id>')
def generate_invoice_pdf(sale_id):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("""
        SELECT s.InvoiceNumber, s.SaleDate, m.Name, s.Products, s.TotalAmount, s.PaidAmount, s.RemainingAmount, u.Username
        FROM Sales s
        JOIN Members m ON s.MemberID = m.ID
        JOIN Users u ON s.UserID = u.ID
        WHERE s.ID = ?
    """, (sale_id,))
    sale = cursor.fetchone()
    products = json.loads(sale.Products)
    product_details = []
    for item in products:
        cursor.execute("SELECT Name, Price FROM Products WHERE ID = ?", (item['id'],))
        product = cursor.fetchone()
        product_details.append({
            'name': product.Name,
            'quantity': item['quantity'],
            'price': product.Price,
            'total': product.Price * item['quantity']
        })
    conn.close()
    rendered = render_template(
        'invoice_pdf.html',
        invoice_number=sale.InvoiceNumber,
        sale_date=sale.SaleDate,
        member_name=sale.Name,
        products=product_details,
        total_amount=sale.TotalAmount,
        paid_amount=sale.PaidAmount,
        remaining_amount=sale.RemainingAmount,
        username=sale.Username,
        today=datetime.now().strftime('%d/%m/%Y')
    )
    config = pdfkit.configuration(wkhtmltopdf=r"C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe")
    options = {'orientation': 'Portrait', 'encoding': 'UTF-8', 'enable-local-file-access': ''}
    pdf = pdfkit.from_string(rendered, False, configuration=config, options=options)
    response = make_response(pdf)
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'inline; filename=invoice_{sale.InvoiceNumber}.pdf'
    return response

# QR Code generation
@app.route('/member/qr/<int:member_id>')
def generate_member_qr(member_id):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT ID, Name, SubscriptionType, StartDate, EndDate FROM Members WHERE ID = ? AND IsActive = 1", (member_id,))
    member = cursor.fetchone()
    if not member:
        conn.close()
        flash("❌ المشترك غير موجود", "error")
        return redirect(url_for('list_members'))
    qr_data = f"Member ID: {member.ID}\nName: {member.Name}\nSubscription: {member.SubscriptionType}\nStart: {member.StartDate}\nEnd: {member.EndDate}"
    qr = qrcode.QRCode(version=1, box_size=10, border=4)
    qr.add_data(qr_data)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")
    img_io = BytesIO()
    img.save(img_io, 'PNG')
    img_io.seek(0)
    conn.close()
    return send_file(img_io, mimetype='image/png', download_name=f'qr_{member.ID}.png')

# Attendance management
@app.route('/attendance', methods=['GET', 'POST'])
def manage_attendance():
    if 'role' not in session or session['role'] not in ['admin', 'editor']:
        return "غير مصرح لك بدخول هذه الصفحة", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    if request.method == 'POST':
        member_id = int(request.form['member_id'])
        action = request.form['action']
        current_time = datetime.now()
        if action == 'check_in':
            cursor.execute("""
                INSERT INTO Attendance (MemberID, AttendanceDate, CheckInTime)
                VALUES (?, ?, ?)
            """, (member_id, current_time.date(), current_time))
        elif action == 'check_out':
            cursor.execute("""
                UPDATE Attendance SET CheckOutTime = ?
                WHERE MemberID = ? AND AttendanceDate = ? AND CheckOutTime IS NULL
            """, (current_time, member_id, current_time.date()))
        conn.commit()
        flash(f"✅ تم تسجيل {'الدخول' if action == 'check_in' else 'الخروج'} بنجاح", "success")
        return redirect(url_for('manage_attendance'))
    cursor.execute("SELECT ID, Name FROM Members WHERE IsActive = 1")
    members = cursor.fetchall()
    cursor.execute("""
        SELECT a.ID, m.Name, a.AttendanceDate, a.CheckInTime, a.CheckOutTime
        FROM Attendance a JOIN Members m ON a.MemberID = m.ID
        ORDER BY a.AttendanceDate DESC, a.CheckInTime DESC
    """)
    attendance_records = cursor.fetchall()
    conn.close()
    return render_template('attendance.html', members=members, attendance_records=attendance_records)

# Reports route
@app.route('/reports', methods=['GET'])
def reports():
    if 'user_id' not in session or not session.get('can_reports', False):
        return "غير مصرح لك بدخول هذه الصفحة", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("""
        SELECT SubscriptionType, COUNT(*) as Count
        FROM Members
        WHERE IsActive = 1
        GROUP BY SubscriptionType
    """)
    subscription_report = cursor.fetchall()
    cursor.execute("""
        SELECT SaleDate, SUM(TotalAmount) as TotalSales
        FROM Sales
        GROUP BY SaleDate
        ORDER BY SaleDate DESC
    """)
    sales_report = cursor.fetchall()
    conn.close()
    return render_template('reports.html', subscription_report=subscription_report, sales_report=sales_report)

# Inactive people route
@app.route('/inactive-people', methods=['GET'])
def inactive_people():
    if 'role' not in session or session['role'] != 'admin':
        return "غير مصرح لك بدخول هذه الصفحة", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("""
        SELECT ID, Name, Phone, SubscriptionType, EndDate
        FROM Members
        WHERE IsActive = 1 AND EndDate < GETDATE()
    """)
    inactive_members = cursor.fetchall()
    conn.close()
    return render_template('inactive_people.html', inactive_members=inactive_members)



@app.route('/inactive', endpoint='inactive_members')
def inactive():
    if 'role' not in session or session['role'] != 'admin':
        return "غير مصرح لك بدخول هذه الصفحة", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT ID, Name, Phone, SubscriptionType, EndDate FROM Members WHERE IsActive = 0")
    members = cursor.fetchall()
    conn.close()
    return render_template('inactive.html', members=members)



# User logs filter route
@app.route('/user_logs_filter', methods=['GET', 'POST'])
def user_logs_filter():
    if 'role' not in session or session['role'] != 'admin':
        return "غير مصرح لك بدخول هذه الصفحة", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    logs = []
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        action = request.form.get('action', '').strip()
        query = "SELECT UserID, Username, ActionType, ScreenName, TransactionName, DeviceName, LogTime FROM UserLogs WHERE 1=1"
        params = []
        if username:
            query += " AND Username LIKE ?"
            params.append(f"%{username}%")
        if action:
            query += " AND ActionType LIKE ?"
            params.append(f"%{action}%")
        cursor.execute(query, params)
        logs = cursor.fetchall()
    conn.close()
    return render_template('user_logs.html', logs=logs)

# Manage users route
@app.route('/manage-users', methods=['GET', 'POST'])
def manage_users():
    if 'role' not in session or session['role'] != 'admin':
        return "غير مصرح لك بدخول هذه الصفحة", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    if request.method == 'POST':
        username = request.form['username']
        password = hashlib.sha256(request.form['password'].encode()).hexdigest()
        role = request.form['role']
        can_search = 1 if 'can_search' in request.form else 0
        can_add_person = 1 if 'can_add_person' in request.form else 0
        can_edit = 1 if 'can_edit' in request.form else 0
        can_delete = 1 if 'can_delete' in request.form else 0
        can_reports = 1 if 'can_reports' in request.form else 0
        can_manage_users = 1 if 'can_manage_users' in request.form else 0
        cursor.execute("""
            INSERT INTO Users (Username, PasswordHash, Role, CanSearch, CanAddPerson, CanEdit, CanDelete, CanReports, CanManageUsers)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (username, password, role, can_search, can_add_person, can_edit, can_delete, can_reports, can_manage_users))
        conn.commit()
        flash("✅ تم إضافة المستخدم بنجاح", "success")
        return redirect(url_for('manage_users'))
    cursor.execute("SELECT ID, Username, Role, CanSearch, CanAddPerson, CanEdit, CanDelete, CanReports, CanManageUsers FROM Users")
    users = cursor.fetchall()
    conn.close()
    return render_template('manage_users.html', users=users)

# System settings route
@app.route('/system-settings', methods=['GET', 'POST'])
def system_settings():
    if 'role' not in session or session['role'] != 'admin':
        return "غير مصرح لك بدخول هذه الصفحة", 403
    conn = get_db_connection()
    cursor = conn.cursor()
    if request.method == 'POST':
        client_name = request.form['client_name']
        theme_color = request.form['theme_color']
        logo = request.files.get('logo')
        logo_path = 'static/logo.png'
        if logo:
            logo.save(os.path.join(app.config['UPLOAD_FOLDER'], 'logo.png'))
        cursor.execute("""
            UPDATE Validation SET ClientName = ?, ThemeColor = ?, LogoPath = ?
            WHERE IsActive = 1
        """, (client_name, theme_color, logo_path))
        conn.commit()
        flash("✅ تم تحديث الإعدادات بنجاح", "success")
        return redirect(url_for('system_settings'))
    cursor.execute("SELECT ClientName, ThemeColor, LogoPath FROM Validation WHERE IsActive = 1")
    settings = cursor.fetchone()
    conn.close()
    return render_template('system_settings.html', settings=settings)

# Change password route
@app.route('/change-password', methods=['GET', 'POST'])
def change_password():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    if request.method == 'POST':
        old_password = request.form['old_password']
        new_password = request.form['new_password']
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT PasswordHash FROM Users WHERE ID = ?", (session['user_id'],))
        user = cursor.fetchone()
        if user and user.PasswordHash == hashlib.sha256(old_password.encode()).hexdigest():
            new_hash = hashlib.sha256(new_password.encode()).hexdigest()
            cursor.execute("UPDATE Users SET PasswordHash = ? WHERE ID = ?", (new_hash, session['user_id']))
            conn.commit()
            flash("✅ تم تغيير كلمة المرور بنجاح", "success")
        else:
            flash("❌ كلمة المرور القديمة غير صحيحة", "error")
        conn.close()
        return redirect(url_for('change_password'))
    return render_template('change_password.html')

# Logging function
def log_user_action(user_id, username, action, screen, transaction="", ip_address=""):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("""
        INSERT INTO UserLogs (UserID, Username, ActionType, ScreenName, TransactionName, DeviceName)
        VALUES (?, ?, ?, ?, ?, ?)
    """, (user_id, username, action, screen, transaction, ip_address))
    conn.commit()
    conn.close()

# Context processors for footer, client name, and settings
@app.context_processor
def inject_footer_data():
    return {
        'today': datetime.now().strftime('%Y-%m-%d'),
        'current_user': session.get('username') or session.get('role') or 'زائر'
    }

def get_client_name():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT TOP 1 ClientName FROM Validation WHERE IsActive = 1")
    result = cursor.fetchone()
    conn.close()
    return result.ClientName if result and result.ClientName else 'اسم العميل'

@app.context_processor
def inject_client_name():
    return {'client_name': get_client_name()}

def get_client_settings():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT TOP 1 ClientName, ThemeColor, LogoPath FROM Validation WHERE IsActive = 1")
    result = cursor.fetchone()
    conn.close()
    return {
        'client_name': result.ClientName if result and result.ClientName else 'اسم العميل',
        'theme_color': result.ThemeColor if result and result.ThemeColor else '#006699',
        'logo_path': result.LogoPath if result and result.LogoPath else 'static/logo.png'
    }

@app.context_processor
def inject_settings():
    settings = get_client_settings()
    return {
        'client_name': settings['client_name'],
        'theme_color': settings['theme_color'],
        'logo_path': settings['logo_path'],
    }

# Test route for debugging
@app.route('/test-login', methods=['GET', 'POST'])
def test_login():
    if request.method == 'POST':
        username = request.form.get('username', '')
        password = request.form.get('password', '')

        # Debug information
        debug_info = {
            'username': username,
            'password_length': len(password) if password else 0,
            'form_data': dict(request.form),
            'method': request.method,
            'content_type': request.content_type
        }

        return f"<pre>{json.dumps(debug_info, indent=2, ensure_ascii=False)}</pre>"

    return '''
    <form method="post">
        <input type="text" name="username" placeholder="Username" required><br><br>
        <input type="password" name="password" placeholder="Password" required><br><br>
        <button type="submit">Test Submit</button>
    </form>
    '''

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=4000)