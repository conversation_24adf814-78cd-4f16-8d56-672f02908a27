{% extends "layout.html" %}
{% block title %}لوحة التحكم - نظام إدارة الصالة الرياضية{% endblock %}
{% block content %}
<!-- Welcome Section -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                مرحباً، {{ session.username | default('المستخدم') }}
            </h1>
            <p class="text-gray-600">إليك نظرة عامة على نشاط الصالة الرياضية اليوم</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <div class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                <i class="fas fa-calendar-alt"></i>
                <span>{{ today }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="mb-8">
    <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
        {% if session.can_add_person %}
        <a href="{{ url_for('add_member') }}" class="quick-action-card group">
            <div class="quick-action-icon bg-blue-500">
                <i class="fas fa-user-plus"></i>
            </div>
            <span class="text-sm font-medium text-gray-700 group-hover:text-blue-600">إضافة مشترك</span>
        </a>
        {% endif %}

        {% if session.can_search %}
        <a href="{{ url_for('search') }}" class="quick-action-card group">
            <div class="quick-action-icon bg-green-500">
                <i class="fas fa-search"></i>
            </div>
            <span class="text-sm font-medium text-gray-700 group-hover:text-green-600">البحث</span>
        </a>
        {% endif %}

        <a href="{{ url_for('list_members') }}" class="quick-action-card group">
            <div class="quick-action-icon bg-purple-500">
                <i class="fas fa-users"></i>
            </div>
            <span class="text-sm font-medium text-gray-700 group-hover:text-purple-600">المشتركين</span>
        </a>

        {% if session.can_reports %}
        <a href="{{ url_for('reports') }}" class="quick-action-card group">
            <div class="quick-action-icon bg-orange-500">
                <i class="fas fa-chart-bar"></i>
            </div>
            <span class="text-sm font-medium text-gray-700 group-hover:text-orange-600">التقارير</span>
        </a>
        {% endif %}
    </div>
</div>

<!-- Statistics Cards -->
<div class="mb-8">
    <h2 class="text-xl font-semibold text-gray-900 mb-6">الإحصائيات العامة</h2>
    <div class="dashboard-grid">
        <!-- Active Members -->
        <div class="stat-card">
            <div class="stat-card-icon bg-blue-500">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-card-title">المشتركين النشطين</div>
            <div class="stat-card-value">{{ total_people }}</div>
            <div class="stat-card-change positive">
                <i class="fas fa-arrow-up"></i>
                نشط حالياً
            </div>
        </div>

        <!-- Expired Subscriptions -->
        <div class="stat-card">
            <div class="stat-card-icon bg-red-500">
                <i class="fas fa-user-times"></i>
            </div>
            <div class="stat-card-title">الإشتراكات المنتهية</div>
            <div class="stat-card-value">{{ total_notactive_people }}</div>
            <div class="stat-card-change negative">
                <i class="fas fa-exclamation-triangle"></i>
                يحتاج تجديد
            </div>
        </div>

        <!-- Trainers -->
        <div class="stat-card">
            <div class="stat-card-icon bg-green-500">
                <i class="fas fa-dumbbell"></i>
            </div>
            <div class="stat-card-title">المدربين</div>
            <div class="stat-card-value">{{ total_servers }}</div>
            <div class="stat-card-change">
                <i class="fas fa-check-circle"></i>
                متاح
            </div>
        </div>

        <!-- Average Subscription -->
        <div class="stat-card">
            <div class="stat-card-icon bg-purple-500">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="stat-card-title">متوسط مدة الإشتراك</div>
            <div class="stat-card-value">{{ avg_stay }}</div>
            <div class="text-sm text-gray-500 mt-1">يوم</div>
        </div>

        <!-- Longest Subscription -->
        <div class="stat-card">
            <div class="stat-card-icon bg-indigo-500">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="stat-card-title">أطول إشتراك</div>
            <div class="stat-card-value">{{ max_stay }}</div>
            <div class="text-sm text-gray-500 mt-1">يوم</div>
        </div>

        {% if avg_age > 0 %}
        <!-- Average Age -->
        <div class="stat-card">
            <div class="stat-card-icon bg-yellow-500">
                <i class="fas fa-birthday-cake"></i>
            </div>
            <div class="stat-card-title">متوسط الأعمار</div>
            <div class="stat-card-value">{{ avg_age }}</div>
            <div class="text-sm text-gray-500 mt-1">سنة</div>
        </div>

        <!-- Youngest Member -->
        <div class="stat-card">
            <div class="stat-card-icon bg-pink-500">
                <i class="fas fa-child"></i>
            </div>
            <div class="stat-card-title">أصغر مشترك</div>
            <div class="stat-card-value">{{ min_age }}</div>
            <div class="text-sm text-gray-500 mt-1">سنة</div>
        </div>

        <!-- Oldest Member -->
        <div class="stat-card">
            <div class="stat-card-icon bg-gray-500">
                <i class="fas fa-user-tie"></i>
            </div>
            <div class="stat-card-title">أكبر مشترك</div>
            <div class="stat-card-value">{{ max_age }}</div>
            <div class="text-sm text-gray-500 mt-1">سنة</div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Recent Activity Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Quick Stats -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">ملخص سريع</h3>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-percentage text-white text-sm"></i>
                        </div>
                        <span class="text-sm font-medium text-gray-700">معدل النشاط</span>
                    </div>
                    <span class="text-lg font-bold text-blue-600">
                        {% if total_people > 0 %}
                            {{ "%.1f"|format(((total_people - total_notactive_people) / total_people * 100)) }}%
                        {% else %}
                            0%
                        {% endif %}
                    </span>
                </div>

                <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-chart-line text-white text-sm"></i>
                        </div>
                        <span class="text-sm font-medium text-gray-700">إجمالي المشتركين</span>
                    </div>
                    <span class="text-lg font-bold text-green-600">{{ total_people + total_notactive_people }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">حالة النظام</h3>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">حالة الخادم</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fas fa-circle text-green-400 mr-1"></i>
                        متصل
                    </span>
                </div>

                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">قاعدة البيانات</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fas fa-circle text-green-400 mr-1"></i>
                        نشطة
                    </span>
                </div>

                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">آخر نسخة احتياطية</span>
                    <span class="text-sm text-gray-900">{{ today }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.quick-action-card {
    @apply flex flex-col items-center p-4 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 transform hover:-translate-y-1;
}

.quick-action-icon {
    @apply w-12 h-12 rounded-full flex items-center justify-center text-white text-lg mb-2;
}
</style>
{% endblock %}