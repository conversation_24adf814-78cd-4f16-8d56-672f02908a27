<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير PDF</title>

    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            padding: 30px;
            color: #222;
            background-color: #fff;
        }

        h2 {
            text-align: center;
            color: #007BFF;
            margin-bottom: 10px;
        }

        p {
            text-align: center;
            font-size: 15px;
            color: #555;
            margin-bottom: 25px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            font-size: 12px;
            word-break: break-word;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px 6px;
            text-align: center;
            vertical-align: middle;
            white-space: normal;
        }

        th {
            background-color: #007BFF;
            color: white;
            font-weight: bold;
        }

        tr:hover {
            background-color: #f9f9f9;
        }

        .col-id {
            width: 40px;
            white-space: nowrap;
        }

        .col-age {
            width: 50px;
            white-space: nowrap;
        }

        .col-name {
            min-width: 180px;
            max-width: 250px;
            white-space: normal;
            word-break: break-word;
        }

        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 13px;
            color: #777;
            border-top: 1px solid #ccc;
            padding-top: 20px;
        }

        @page {
            size: A4 landscape;
            margin: 20mm;
        }

        @media print {
            body {
                background: white;
                color: black;
            }
        }
    </style>
</head>
<body>

<h2>
    {% if report_type == 'inactive' %}
        🛑 تقرير الأعضاء المنتهية إشتراكاتهم
    {% else %}
        📄 نتائج البحث - <span>{{ client_name }}</span>

    {% endif %}
</h2>

    {% if report_type != 'inactive' %}
        <p>
            {% if name %}الاسم: {{ name }}{% endif %}
            {% if exam_date %}، تاريخ الكشف: {{ exam_date }}{% endif %}
            {% if min_age %}، العمر من: {{ min_age }}{% endif %}
            {% if max_age %}، إلى: {{ max_age }}{% endif %}
            {% if treating_doctor %}، الطبيب المعالج: {{ treating_doctor }}{% endif %}
            {% if responsible_server %}، الخادم المسئول: {{ responsible_server }}{% endif %}
        </p>
    {% endif %}

<table>
    <thead>
        <tr>
            <th class="col-id">ID</th>
            <th class="col-name">الاسم</th>
            {% if report_type != 'inactive' %}
                <th class="col-age">السن</th>
            {% endif %}
            <th>تاريخ الدخول</th>
            {% if report_type != 'inactive' %}
                <th>مدة الإقامة</th>
            {% endif %}
            <th>تاريخ الكشف</th>
            <th>تاريخ الإضافة</th>
            <th>العنوان</th>
            <th>الخادم المسئول</th>
            <th>تليفون الخادم</th>
            <th>نوع الإعاقة</th>
            <th>الطبيب المعالج</th>
            <th>الأدوية المتكررة</th>
            <th>ملاحظات</th>
        </tr>
    </thead>
    <tbody>
        {% for r in results %}
        <tr>
            <td class="col-id">{{ r.ID }}</td>
            <td class="col-name">{{ r.Name }}</td>
            {% if report_type != 'inactive' %}
                <td class="col-age">{{ r.Age }}</td>
            {% endif %}
            <td>{{ r.EntryDate }}</td>
            {% if report_type != 'inactive' %}
                <td>{{ r.StayDays or 0 }} يوم</td>
            {% endif %}
            <td>{{ r.ExamDate }}</td>
            <td>{{ r.DateAdded }}</td>
            <td>{{ r.Address or 'لا يوجد' }}</td>
            <td>{{ r.ResponsibleServerName or 'لا يوجد' }}</td>
            <td>{{ r.ServerPhoneNumber or 'لا يوجد' }}</td>
            <td>{{ r.DisabilityType or 'لا يوجد' }}</td>
            <td>{{ r.TreatingDoctor or 'لا يوجد' }}</td>
            <td>{{ r.FrequentMedications or 'لا يوجد' }}</td>
            <td>{{ r.Notes or 'لا يوجد' }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<div class="footer">
    تم إعداد هذا التقرير بواسطة {{ session.username or session.role or 'النظام' }}<br>
    التاريخ: {{ today }}<br>
    © حقوق الطبع محفوظة - Spring Soft
</div>

</body>
</html>
