/**
 * Spring GYM System - Main JavaScript File
 * Handles mobile interactions, form validation, and user feedback
 */

// Global app object
window.SpringGYM = {
    // Configuration
    config: {
        animationDuration: 300,
        debounceDelay: 300,
        apiTimeout: 10000
    },

    // Initialize the application
    init: function() {
        this.initMobileMenu();
        this.initFormValidation();
        this.initTableResponsiveness();
        this.initTooltips();
        this.initLoadingStates();
        this.initNotifications();
        console.log('Spring GYM System initialized');
    },

    // Mobile menu functionality
    initMobileMenu: function() {
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');

        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', () => {
                this.toggleMobileMenu();
            });

            if (mobileMenuOverlay) {
                mobileMenuOverlay.addEventListener('click', () => {
                    this.closeMobileMenu();
                });
            }

            // Close menu on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && mobileMenu.classList.contains('translate-x-0')) {
                    this.closeMobileMenu();
                }
            });
        }
    },

    toggleMobileMenu: function() {
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        
        if (mobileMenu.classList.contains('translate-x-0')) {
            this.closeMobileMenu();
        } else {
            this.openMobileMenu();
        }
    },

    openMobileMenu: function() {
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        
        mobileMenu.classList.remove('translate-x-full');
        mobileMenu.classList.add('translate-x-0');
        
        if (mobileMenuOverlay) {
            mobileMenuOverlay.classList.remove('hidden');
            mobileMenuOverlay.classList.add('opacity-50');
        }
        
        document.body.style.overflow = 'hidden';
    },

    closeMobileMenu: function() {
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        
        mobileMenu.classList.remove('translate-x-0');
        mobileMenu.classList.add('translate-x-full');
        
        if (mobileMenuOverlay) {
            mobileMenuOverlay.classList.remove('opacity-50');
            setTimeout(() => {
                mobileMenuOverlay.classList.add('hidden');
            }, this.config.animationDuration);
        }
        
        document.body.style.overflow = '';
    },

    // Form validation functionality
    initFormValidation: function() {
        const forms = document.querySelectorAll('form[data-validate]');
        
        forms.forEach(form => {
            this.setupFormValidation(form);
        });
    },

    setupFormValidation: function(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            // Real-time validation
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', this.debounce(() => {
                this.validateField(input);
            }, this.config.debounceDelay));
        });

        // Form submission
        form.addEventListener('submit', (e) => {
            if (!this.validateForm(form)) {
                e.preventDefault();
                this.showFormErrors(form);
            } else {
                this.showLoadingState(form);
            }
        });
    },

    validateField: function(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');
        let isValid = true;
        let errorMessage = '';

        // Required validation
        if (required && !value) {
            isValid = false;
            errorMessage = 'هذا الحقل مطلوب';
        }

        // Type-specific validation
        if (value && isValid) {
            switch (type) {
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'البريد الإلكتروني غير صحيح';
                    }
                    break;
                
                case 'tel':
                    const phoneRegex = /^(05|5)[0-9]{8}$/;
                    if (!phoneRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'رقم الهاتف غير صحيح (مثال: 0501234567)';
                    }
                    break;
                
                case 'number':
                    const min = parseFloat(field.getAttribute('min'));
                    const max = parseFloat(field.getAttribute('max'));
                    const numValue = parseFloat(value);
                    
                    if (isNaN(numValue)) {
                        isValid = false;
                        errorMessage = 'يجب إدخال رقم صحيح';
                    } else if (min !== null && numValue < min) {
                        isValid = false;
                        errorMessage = `القيمة يجب أن تكون أكبر من أو تساوي ${min}`;
                    } else if (max !== null && numValue > max) {
                        isValid = false;
                        errorMessage = `القيمة يجب أن تكون أصغر من أو تساوي ${max}`;
                    }
                    break;
                
                case 'date':
                    const dateValue = new Date(value);
                    const today = new Date();
                    
                    if (field.hasAttribute('max') && field.getAttribute('max') === 'today') {
                        if (dateValue > today) {
                            isValid = false;
                            errorMessage = 'التاريخ لا يمكن أن يكون في المستقبل';
                        }
                    }
                    break;
            }
        }

        // Custom validation
        if (field.hasAttribute('data-validate-custom')) {
            const customValidation = field.getAttribute('data-validate-custom');
            const customResult = this.customValidation(field, customValidation);
            if (!customResult.isValid) {
                isValid = false;
                errorMessage = customResult.message;
            }
        }

        this.showFieldValidation(field, isValid, errorMessage);
        return isValid;
    },

    customValidation: function(field, validationType) {
        switch (validationType) {
            case 'password-strength':
                const password = field.value;
                if (password.length < 8) {
                    return { isValid: false, message: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' };
                }
                if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
                    return { isValid: false, message: 'كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام' };
                }
                break;
            
            case 'confirm-password':
                const originalPassword = document.querySelector('[name="password"]');
                if (originalPassword && field.value !== originalPassword.value) {
                    return { isValid: false, message: 'كلمة المرور غير متطابقة' };
                }
                break;
        }
        
        return { isValid: true, message: '' };
    },

    showFieldValidation: function(field, isValid, errorMessage) {
        const errorElement = field.parentNode.querySelector('.form-error');
        
        // Remove existing classes
        field.classList.remove('border-red-500', 'border-green-500');
        
        if (isValid) {
            field.classList.add('border-green-500');
            if (errorElement) {
                errorElement.classList.add('hidden');
            }
        } else {
            field.classList.add('border-red-500');
            if (errorElement) {
                errorElement.textContent = errorMessage;
                errorElement.classList.remove('hidden');
            }
        }
    },

    validateForm: function(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        let isFormValid = true;
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isFormValid = false;
            }
        });
        
        return isFormValid;
    },

    showFormErrors: function(form) {
        const firstError = form.querySelector('.border-red-500');
        if (firstError) {
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstError.focus();
        }
    },

    showLoadingState: function(form) {
        const submitButton = form.querySelector('button[type="submit"]');
        const spinner = form.querySelector('.spinner, #submitSpinner');
        
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.classList.add('opacity-75');
        }
        
        if (spinner) {
            spinner.classList.remove('hidden');
        }
    },

    // Table responsiveness
    initTableResponsiveness: function() {
        const viewToggle = document.getElementById('viewToggle');
        if (viewToggle) {
            viewToggle.addEventListener('click', this.toggleTableView.bind(this));
        }
        
        // Initialize view based on screen size
        this.updateTableView();
        
        // Handle window resize
        window.addEventListener('resize', this.debounce(() => {
            this.updateTableView();
        }, 250));
    },

    toggleTableView: function() {
        const tableView = document.getElementById('tableView');
        const cardView = document.getElementById('cardView');
        const viewIcon = document.getElementById('viewIcon');
        const viewText = document.getElementById('viewText');
        
        if (tableView && cardView) {
            const isTableVisible = !tableView.classList.contains('hidden');
            
            if (isTableVisible) {
                tableView.classList.add('hidden');
                cardView.classList.remove('hidden');
                if (viewIcon) viewIcon.className = 'fas fa-table';
                if (viewText) viewText.textContent = 'عرض الجدول';
            } else {
                tableView.classList.remove('hidden');
                cardView.classList.add('hidden');
                if (viewIcon) viewIcon.className = 'fas fa-th-list';
                if (viewText) viewText.textContent = 'عرض البطاقات';
            }
        }
    },

    updateTableView: function() {
        const tableView = document.getElementById('tableView');
        const cardView = document.getElementById('cardView');
        
        if (tableView && cardView) {
            if (window.innerWidth < 1024) {
                tableView.classList.add('hidden');
                cardView.classList.remove('hidden');
            } else {
                tableView.classList.remove('hidden');
                cardView.classList.add('hidden');
            }
        }
    },

    // Utility functions
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Initialize tooltips
    initTooltips: function() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    },

    showTooltip: function(e) {
        const element = e.target;
        const tooltipText = element.getAttribute('data-tooltip');
        
        if (tooltipText) {
            const tooltip = document.createElement('div');
            tooltip.className = 'absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg';
            tooltip.textContent = tooltipText;
            tooltip.id = 'tooltip';
            
            document.body.appendChild(tooltip);
            
            const rect = element.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
        }
    },

    hideTooltip: function() {
        const tooltip = document.getElementById('tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    },

    // Loading states
    initLoadingStates: function() {
        // Add loading states to buttons with data-loading attribute
        const loadingButtons = document.querySelectorAll('[data-loading]');
        
        loadingButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.showButtonLoading(button);
            });
        });
    },

    showButtonLoading: function(button) {
        const originalText = button.innerHTML;
        button.setAttribute('data-original-text', originalText);
        button.innerHTML = '<div class="spinner spinner-sm mr-2"></div> جاري التحميل...';
        button.disabled = true;
        
        // Auto-restore after 10 seconds (fallback)
        setTimeout(() => {
            this.hideButtonLoading(button);
        }, 10000);
    },

    hideButtonLoading: function(button) {
        const originalText = button.getAttribute('data-original-text');
        if (originalText) {
            button.innerHTML = originalText;
            button.disabled = false;
            button.removeAttribute('data-original-text');
        }
    },

    // Notifications
    initNotifications: function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert-success, .alert-error, .alert-warning');
        
        alerts.forEach(alert => {
            setTimeout(() => {
                this.fadeOutElement(alert);
            }, 5000);
        });
    },

    fadeOutElement: function(element) {
        element.style.transition = 'opacity 0.5s ease';
        element.style.opacity = '0';
        
        setTimeout(() => {
            element.remove();
        }, 500);
    },

    // Show notification
    showNotification: function(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `alert-${type} fixed top-4 right-4 z-50 max-w-sm`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            ${message}
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            this.fadeOutElement(notification);
        }, 5000);
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    SpringGYM.init();
});

// Export for use in other scripts
window.SpringGYM = SpringGYM;
