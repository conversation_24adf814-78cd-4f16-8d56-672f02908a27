<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - نظام إدارة الصالة الرياضية</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Noto Sans Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            direction: rtl;
        }
        
        .offline-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #006699, #004477);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md mx-auto text-center p-8">
        <!-- Offline Icon -->
        <div class="mb-8">
            <div class="w-24 h-24 mx-auto bg-red-100 rounded-full flex items-center justify-center offline-animation">
                <i class="fas fa-wifi-slash text-4xl text-red-600"></i>
            </div>
        </div>
        
        <!-- Title -->
        <h1 class="text-3xl font-bold text-gray-900 mb-4">
            غير متصل بالإنترنت
        </h1>
        
        <!-- Description -->
        <p class="text-gray-600 mb-8 leading-relaxed">
            يبدو أنك غير متصل بالإنترنت حالياً. تحقق من اتصالك بالشبكة وحاول مرة أخرى.
        </p>
        
        <!-- Connection Status -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
                <span class="text-sm font-medium text-gray-700">حالة الاتصال:</span>
                <span id="connection-status" class="text-sm font-medium text-red-600">غير متصل</span>
            </div>
            
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div id="connection-bar" class="bg-red-500 h-2 rounded-full w-0 transition-all duration-500"></div>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="space-y-4">
            <!-- Retry Button -->
            <button onclick="checkConnection()" 
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center">
                <i class="fas fa-sync-alt ml-2"></i>
                إعادة المحاولة
            </button>
            
            <!-- Go Back Button -->
            <button onclick="goBack()" 
                    class="w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة للخلف
            </button>
            
            <!-- Home Button -->
            <a href="/" 
               class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center">
                <i class="fas fa-home ml-2"></i>
                الصفحة الرئيسية
            </a>
        </div>
        
        <!-- Tips -->
        <div class="mt-12 text-right">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">نصائح للاتصال:</h3>
            <ul class="text-sm text-gray-600 space-y-2">
                <li class="flex items-start">
                    <i class="fas fa-check text-green-500 ml-2 mt-0.5"></i>
                    تأكد من تشغيل الواي فاي أو البيانات الخلوية
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-green-500 ml-2 mt-0.5"></i>
                    تحقق من قوة الإشارة
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-green-500 ml-2 mt-0.5"></i>
                    جرب إعادة تشغيل المتصفح
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check text-green-500 ml-2 mt-0.5"></i>
                    تواصل مع مزود الخدمة إذا استمرت المشكلة
                </li>
            </ul>
        </div>
        
        <!-- Footer -->
        <div class="mt-12 pt-8 border-t border-gray-200">
            <div class="flex items-center justify-center text-gray-500">
                <i class="fas fa-dumbbell ml-2"></i>
                <span class="text-sm">نظام إدارة الصالة الرياضية - Spring GYM</span>
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connection-status');
            const connectionBar = document.getElementById('connection-bar');
            
            if (navigator.onLine) {
                statusElement.textContent = 'متصل';
                statusElement.className = 'text-sm font-medium text-green-600';
                connectionBar.className = 'bg-green-500 h-2 rounded-full w-full transition-all duration-500';
                
                // Auto-redirect when back online
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                statusElement.textContent = 'غير متصل';
                statusElement.className = 'text-sm font-medium text-red-600';
                connectionBar.className = 'bg-red-500 h-2 rounded-full w-0 transition-all duration-500';
            }
        }
        
        // Manual connection check
        function checkConnection() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الفحص...';
            button.disabled = true;
            
            // Simulate checking
            setTimeout(() => {
                updateConnectionStatus();
                button.innerHTML = originalText;
                button.disabled = false;
                
                if (navigator.onLine) {
                    window.location.reload();
                }
            }, 1500);
        }
        
        // Go back function
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }
        
        // Listen for online/offline events
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);
    </script>
</body>
</html>
