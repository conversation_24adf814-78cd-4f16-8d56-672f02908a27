{% extends "layout.html" %}
{% block content %}
<h2>👥 إدارة المستخدمين</h2>
{% if session.get('success') %}
<div class="alert alert-success">{{ session.get('success') }}</div>
{% endif %}
<form method="POST">
    <label>اسم المستخدم:</label>
    <input type="text" name="username" required>
    <label>كلمة المرور:</label>
    <input type="password" name="password" required>
    <label>الدور:</label>
    <select name="role" required>
        <option value="admin">مدير</option>
        <option value="editor">محرر</option>
        <option value="viewer">عارض</option>
    </select>
    <label>الصلاحيات:</label>
    <input type="checkbox" name="can_search"> البحث
    <input type="checkbox" name="can_add_person"> إضافة مشترك
    <input type="checkbox" name="can_edit"> تعديل
    <input type="checkbox" name="can_delete"> حذف
    <input type="checkbox" name="can_reports"> التقارير
    <input type="checkbox" name="can_manage_users"> إدارة المستخدمين
    <button type="submit">💾 إضافة مستخدم</button>
</form>
<table>
    <thead>
        <tr>
            <th>اسم المستخدم</th>
            <th>الدور</th>
            <th>الصلاحيات</th>
        </tr>
    </thead>
    <tbody>
        {% for u in users %}
        <tr>
            <td>{{ u.Username }}</td>
            <td>{{ u.Role }}</td>
            <td>
                {% if u.CanSearch %}البحث, {% endif %}
                {% if u.CanAddPerson %}إضافة, {% endif %}
                {% if u.CanEdit %}تعديل, {% endif %}
                {% if u.CanDelete %}حذف, {% endif %}
                {% if u.CanReports %}تقارير, {% endif %}
                {% if u.CanManageUsers %}إدارة المستخدمين{% endif %}
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}